-- Reset and Fix Driver License Storage Policies
-- Execute this script in Supabase SQL Editor to fix the RLS policy issues

-- =====================================================
-- STEP 1: CLEAN UP EXISTING POLICIES
-- =====================================================

-- Drop all existing policies for driver-licenses bucket
DROP POLICY IF EXISTS "Users can upload their own license documents" ON storage.objects;
DROP POLICY IF EXISTS "Users can read their own license documents" ON storage.objects;
DROP POLICY IF EXISTS "Users can update their own license documents" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete their own license documents" ON storage.objects;
DROP POLICY IF EXISTS "Admins can read all license documents" ON storage.objects;

-- Drop any other conflicting policies
DROP POLICY IF EXISTS "driver_license_upload_policy" ON storage.objects;
DROP POLICY IF EXISTS "driver_license_read_policy" ON storage.objects;
DROP POLICY IF EXISTS "driver_license_update_policy" ON storage.objects;
DROP POLICY IF EXISTS "driver_license_delete_policy" ON storage.objects;

-- =====================================================
-- STEP 2: ENSURE BUCKET EXISTS
-- =====================================================

-- Create driver-licenses bucket if it doesn't exist
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'driver-licenses', 
  'driver-licenses', 
  false, 
  5242880, -- 5MB limit
  ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
)
ON CONFLICT (id) DO UPDATE SET
  name = 'driver-licenses',
  public = false,
  file_size_limit = 5242880,
  allowed_mime_types = ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];

-- =====================================================
-- STEP 3: CREATE NEW WORKING POLICIES
-- =====================================================

-- Policy 1: Allow users to upload their own license documents
CREATE POLICY "driver_license_upload_policy"
ON storage.objects
FOR INSERT
WITH CHECK (
  bucket_id = 'driver-licenses'
  AND auth.role() = 'authenticated'
  AND name ~ ('^licenses/license_' || auth.uid()::text || '\.(jpg|jpeg|png|webp)$')
  AND (metadata->>'owner')::uuid = auth.uid()
);

-- Policy 2: Allow users to read their own license documents
CREATE POLICY "driver_license_read_policy"
ON storage.objects
FOR SELECT
USING (
  bucket_id = 'driver-licenses'
  AND auth.role() = 'authenticated'
  AND (
    name ~ ('^licenses/license_' || auth.uid()::text || '\.(jpg|jpeg|png|webp)$')
    OR (metadata->>'owner')::uuid = auth.uid()
  )
);

-- Policy 3: Allow users to update their own license documents
CREATE POLICY "driver_license_update_policy"
ON storage.objects
FOR UPDATE
USING (
  bucket_id = 'driver-licenses'
  AND auth.role() = 'authenticated'
  AND (
    name ~ ('^licenses/license_' || auth.uid()::text || '\.(jpg|jpeg|png|webp)$')
    OR (metadata->>'owner')::uuid = auth.uid()
  )
);

-- Policy 4: Allow users to delete their own license documents
CREATE POLICY "driver_license_delete_policy"
ON storage.objects
FOR DELETE
USING (
  bucket_id = 'driver-licenses'
  AND auth.role() = 'authenticated'
  AND (
    name ~ ('^licenses/license_' || auth.uid()::text || '\.(jpg|jpeg|png|webp)$')
    OR (metadata->>'owner')::uuid = auth.uid()
  )
);

-- Policy 5: Allow admins to read all license documents (optional)
CREATE POLICY "admin_license_read_policy"
ON storage.objects
FOR SELECT
USING (
  bucket_id = 'driver-licenses' 
  AND auth.role() = 'authenticated'
  AND name LIKE 'licenses/%'
  AND EXISTS (
    SELECT 1 FROM public.users 
    WHERE id = auth.uid() 
    AND (role = 'admin' OR role = 'super_admin')
  )
);

-- =====================================================
-- STEP 4: ENABLE RLS
-- =====================================================

-- Ensure RLS is enabled on storage.objects
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- STEP 5: VERIFICATION QUERIES
-- =====================================================

-- Verify bucket configuration
SELECT 
  id, 
  name, 
  public, 
  file_size_limit,
  allowed_mime_types,
  created_at 
FROM storage.buckets 
WHERE id = 'driver-licenses';

-- Verify policies are created
SELECT 
  schemaname, 
  tablename, 
  policyname, 
  permissive, 
  roles, 
  cmd, 
  qual
FROM pg_policies 
WHERE schemaname = 'storage' 
  AND tablename = 'objects'
  AND policyname LIKE '%license%'
ORDER BY policyname;

-- Test policy with sample user ID (replace with actual user ID for testing)
-- SELECT 'licenses/license_550e8400-e29b-41d4-a716-************.jpg' ~ ('^licenses/license_550e8400-e29b-41d4-a716-************\.(jpg|jpeg|png|webp)$') AS policy_match;

-- =====================================================
-- STEP 6: TROUBLESHOOTING QUERIES
-- =====================================================

-- Check if RLS is enabled
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'storage' AND tablename = 'objects';

-- List all storage policies
SELECT policyname, cmd, qual 
FROM pg_policies 
WHERE schemaname = 'storage' AND tablename = 'objects'
ORDER BY policyname;

-- Check bucket permissions
SELECT * FROM storage.buckets WHERE id = 'driver-licenses';

-- =====================================================
-- NOTES FOR DEVELOPERS
-- =====================================================

/*
File Path Pattern:
- Upload path: licenses/license_<user_id>.<extension>
- Example: licenses/license_550e8400-e29b-41d4-a716-************.jpg

Policy Pattern:
- Uses regex: ^licenses/license_<auth.uid()>\.(jpg|jpeg|png|webp)$
- Ensures exact match with user ID and valid extensions

Security Features:
- Only authenticated users can access
- Users can only access their own files
- Admins can read all files for verification
- File size limited to 5MB
- Only image files allowed

Testing:
1. Ensure user is authenticated
2. Upload file with correct path pattern
3. Verify file appears in bucket
4. Test signed URL generation
5. Verify cross-user access is denied
*/
