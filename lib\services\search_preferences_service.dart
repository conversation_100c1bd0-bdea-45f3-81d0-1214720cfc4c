import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SearchPreferencesService {
  static final SearchPreferencesService _instance =
      SearchPreferencesService._internal();
  factory SearchPreferencesService() => _instance;
  SearchPreferencesService._internal();

  static const String _recentSearchesKey = 'recent_city_searches';
  static const int _maxRecentSearches = 3;

  SharedPreferences? _prefs;

  /// Initialize SharedPreferences
  Future<void> init() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  /// Add a city to recent searches
  Future<void> addRecentSearch(String city) async {
    await init();

    if (city.trim().isEmpty) return;

    try {
      final recentSearches = await getRecentSearches();

      // Remove if already exists to avoid duplicates
      recentSearches
          .removeWhere((search) => search.toLowerCase() == city.toLowerCase());

      // Add to the beginning
      recentSearches.insert(0, city.trim());

      // Keep only the last N searches
      if (recentSearches.length > _maxRecentSearches) {
        recentSearches.removeRange(_maxRecentSearches, recentSearches.length);
      }

      // Save to preferences
      await _prefs!.setStringList(_recentSearchesKey, recentSearches);

      if (kDebugMode) {
        print('💾 Added recent search: "$city"');
        print('📝 Recent searches: ${recentSearches.join(", ")}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to add recent search: $e');
      }
    }
  }

  /// Get recent searches
  Future<List<String>> getRecentSearches() async {
    await init();

    try {
      final searches = _prefs!.getStringList(_recentSearchesKey) ?? [];

      if (kDebugMode) {
        print('📖 Retrieved recent searches: ${searches.join(", ")}');
      }

      return searches;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to get recent searches: $e');
      }
      return [];
    }
  }

  /// Clear all recent searches
  Future<void> clearRecentSearches() async {
    await init();

    try {
      await _prefs!.remove(_recentSearchesKey);

      if (kDebugMode) {
        print('🧹 Cleared all recent searches');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to clear recent searches: $e');
      }
    }
  }

  /// Remove a specific recent search
  Future<void> removeRecentSearch(String city) async {
    await init();

    try {
      final recentSearches = await getRecentSearches();
      recentSearches
          .removeWhere((search) => search.toLowerCase() == city.toLowerCase());

      await _prefs!.setStringList(_recentSearchesKey, recentSearches);

      if (kDebugMode) {
        print('🗑️ Removed recent search: "$city"');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to remove recent search: $e');
      }
    }
  }

  /// Check if a city is in recent searches
  Future<bool> isRecentSearch(String city) async {
    final recentSearches = await getRecentSearches();
    return recentSearches
        .any((search) => search.toLowerCase() == city.toLowerCase());
  }

  /// Get recent searches count
  Future<int> getRecentSearchesCount() async {
    final recentSearches = await getRecentSearches();
    return recentSearches.length;
  }

  /// Get search statistics for debugging
  Future<Map<String, dynamic>> getSearchStats() async {
    final recentSearches = await getRecentSearches();

    return {
      'recent_searches_count': recentSearches.length,
      'max_recent_searches': _maxRecentSearches,
      'recent_searches': recentSearches,
      'preferences_initialized': _prefs != null,
    };
  }
}
