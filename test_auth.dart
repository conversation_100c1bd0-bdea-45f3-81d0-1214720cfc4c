// Test script for custom authentication
// Run this in Dart console or as a Flutter test

import 'package:flutter/foundation.dart';

void main() {
  print('🧪 Testing Custom Authentication System');
  print('=====================================');
  
  // Test phone normalization
  testPhoneNormalization();
  
  // Test authentication flow
  testAuthFlow();
  
  print('\n✅ All tests completed!');
}

void testPhoneNormalization() {
  print('\n📱 Testing Phone Normalization:');
  
  final testCases = [
    '+212*********',
    '0*********', 
    '*********',
    '+212 612 345 678',
    '0612-345-678',
  ];
  
  for (String phone in testCases) {
    final normalized = normalizePhone(phone);
    print('  $phone → $normalized');
  }
}

String normalizePhone(String phone) {
  // Remove all non-digit characters
  final cleanPhone = phone.replaceAll(RegExp(r'[^\d]'), '');

  // Convert to consistent format: remove leading +212 or 0
  String normalizedPhone = cleanPhone;
  if (normalizedPhone.startsWith('212')) {
    normalizedPhone = normalizedPhone.substring(3);
  } else if (normalizedPhone.startsWith('0')) {
    normalizedPhone = normalizedPhone.substring(1);
  }

  return normalizedPhone;
}

void testAuthFlow() {
  print('\n🔐 Testing Authentication Flow:');
  
  // Test signup data
  final signupData = {
    'phone': '*********',
    'full_name': 'تجربة مستخدم',
    'password': '123456',
    'role': 'traveler',
  };
  
  print('  Signup Data: $signupData');
  
  // Test login data
  final loginData = {
    'phone': '*********',
    'password': '123456',
  };
  
  print('  Login Data: $loginData');
  
  // Test error cases
  final errorCases = [
    {'phone': '*********', 'password': '123456', 'expected': 'رقم الهاتف غير مسجل'},
    {'phone': '*********', 'password': 'wrong', 'expected': 'كلمة المرور غير صحيحة'},
  ];
  
  print('  Error Cases:');
  for (var errorCase in errorCases) {
    print('    ${errorCase['phone']} / ${errorCase['password']} → ${errorCase['expected']}');
  }
}
