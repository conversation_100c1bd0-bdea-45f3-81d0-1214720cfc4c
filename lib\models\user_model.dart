import 'package:supabase_flutter/supabase_flutter.dart';

class UserModel {
  final String id;
  final String? email; // Made optional for future email verification
  final String phone; // Now required as primary identifier
  final String fullName;
  final String role; // 'trip_leader' or 'traveler'
  final String? profileImageUrl;
  final String? bio;
  final String? city;
  final DateTime? dateOfBirth;
  final String? gender; // 'male', 'female'
  final bool isVerified;
  final bool isLeader; // New field for trip leader status
  final double balance; // New field for leader balance
  final double rating;
  final int totalTrips;
  final int totalRatings;
  final List<String> badges;
  final Map<String, dynamic>? preferences;
  final DateTime createdAt;
  final DateTime updatedAt;

  UserModel({
    required this.id,
    this.email,
    required this.phone,
    required this.fullName,
    required this.role,
    this.profileImageUrl,
    this.bio,
    this.city,
    this.dateOfBirth,
    this.gender,
    this.isVerified = false,
    this.isLeader = false,
    this.balance = 0.0,
    this.rating = 0.0,
    this.totalTrips = 0,
    this.totalRatings = 0,
    this.badges = const [],
    this.preferences,
    required this.createdAt,
    required this.updatedAt,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] as String,
      email: json['email'] as String?,
      phone: json['phone'] as String,
      fullName: json['full_name'] as String,
      role: json['role'] as String,
      profileImageUrl: json['profile_image_url'] as String?,
      bio: json['bio'] as String?,
      city: json['city'] as String?,
      dateOfBirth: json['date_of_birth'] != null
          ? DateTime.parse(json['date_of_birth'] as String)
          : null,
      gender: json['gender'] as String?,
      isVerified: json['is_verified'] as bool? ?? false,
      isLeader: json['is_leader'] as bool? ?? false,
      balance: (json['balance'] as num?)?.toDouble() ?? 0.0,
      rating: (json['rating'] as num?)?.toDouble() ?? 0.0,
      totalTrips: json['total_trips'] as int? ?? 0,
      totalRatings: json['total_ratings'] as int? ?? 0,
      badges: json['badges'] != null
          ? List<String>.from(json['badges'] as List)
          : [],
      preferences: json['preferences'] as Map<String, dynamic>?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'phone': phone,
      'full_name': fullName,
      'role': role,
      'profile_image_url': profileImageUrl,
      'bio': bio,
      'city': city,
      'date_of_birth': dateOfBirth?.toIso8601String(),
      'gender': gender,
      'is_verified': isVerified,
      'is_leader': isLeader,
      'balance': balance,
      'rating': rating,
      'total_trips': totalTrips,
      'total_ratings': totalRatings,
      'badges': badges,
      'preferences': preferences,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  UserModel copyWith({
    String? id,
    String? email,
    String? phone,
    String? fullName,
    String? role,
    String? profileImageUrl,
    String? bio,
    String? city,
    DateTime? dateOfBirth,
    String? gender,
    bool? isVerified,
    bool? isLeader,
    double? balance,
    double? rating,
    int? totalTrips,
    int? totalRatings,
    List<String>? badges,
    Map<String, dynamic>? preferences,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      fullName: fullName ?? this.fullName,
      role: role ?? this.role,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      bio: bio ?? this.bio,
      city: city ?? this.city,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      gender: gender ?? this.gender,
      isVerified: isVerified ?? this.isVerified,
      isLeader: isLeader ?? this.isLeader,
      balance: balance ?? this.balance,
      rating: rating ?? this.rating,
      totalTrips: totalTrips ?? this.totalTrips,
      totalRatings: totalRatings ?? this.totalRatings,
      badges: badges ?? this.badges,
      preferences: preferences ?? this.preferences,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  bool get isTripLeader => role == 'trip_leader';
  bool get isTraveler => role == 'traveler';
  bool get canCreateTrips => isLeader && balance >= 5.0;
  bool get hasMinimumBalance => balance >= 5.0;

  String get displayRating => rating > 0 ? rating.toStringAsFixed(1) : 'جديد';
  String get displayBalance => '${balance.toStringAsFixed(2)} درهم';

  int get age {
    if (dateOfBirth == null) return 0;
    final now = DateTime.now();
    int age = now.year - dateOfBirth!.year;
    if (now.month < dateOfBirth!.month ||
        (now.month == dateOfBirth!.month && now.day < dateOfBirth!.day)) {
      age--;
    }
    return age;
  }
}
