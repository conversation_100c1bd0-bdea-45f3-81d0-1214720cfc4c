# Metadata Upload Fix for Driver License - إصلاح رفع البيانات الوصفية لرخصة القيادة

## Problem Description / وصف المشكلة

The driver license upload was failing with a 403 error due to RLS policy requirements:

```
Error uploading driver license: StorageException(message: new row violates row-level security policy, statusCode: 403, error: Unauthorized)
```

The RLS policy requires uploaded files to have metadata with an "owner" field matching the current user's UID.

كان رفع رخصة القيادة يفشل مع خطأ 403 بسبب متطلبات سياسة RLS التي تتطلب أن تحتوي الملفات المرفوعة على بيانات وصفية مع حقل "owner" يطابق معرف المستخدم الحالي.

## Root Cause / السبب الجذري

### 1. Missing Metadata in Upload / عدم وجود بيانات وصفية في الرفع

**Problem:**
- FileOptions was missing the required metadata field
- RLS policy expected `metadata->>'owner' = auth.uid()`
- Upload was failing security check

### 2. Incorrect Content Type / نوع المحتوى غير صحيح

**Problem:**
- No content type specified in FileOptions
- Supabase couldn't properly identify file type
- Missing proper file handling

## Complete Solution / الحل الكامل

### 1. Updated Upload Method with Metadata / طريقة رفع محدثة مع البيانات الوصفية

#### **Before (Missing Metadata):** ❌
```dart
const uploadOptions = FileOptions(
  cacheControl: '3600',
  upsert: true,
  // ❌ Missing metadata and content type
);
```

#### **After (With Required Metadata):** ✅
```dart
// Determine content type based on file extension
String contentType;
switch (fileExtension) {
  case 'jpg':
  case 'jpeg':
    contentType = 'image/jpeg';
    break;
  case 'png':
    contentType = 'image/png';
    break;
  case 'webp':
    contentType = 'image/webp';
    break;
  default:
    contentType = 'image/jpeg';
}

// Upload with proper options including metadata for RLS policy
final uploadOptions = FileOptions(
  cacheControl: '3600',
  upsert: true,
  contentType: contentType,
  metadata: {
    'owner': currentUser.id, // ✅ Required for RLS policy
    'uploaded_by': userId,
    'file_type': 'driver_license',
    'upload_timestamp': DateTime.now().toIso8601String(),
  },
);
```

### 2. Enhanced RLS Policies / سياسات RLS محسنة

#### **Upload Policy with Metadata Check:**
```sql
CREATE POLICY "driver_license_upload_policy"
ON storage.objects
FOR INSERT
WITH CHECK (
  bucket_id = 'driver-licenses' 
  AND auth.role() = 'authenticated'
  AND name ~ ('^licenses/license_' || auth.uid()::text || '\.(jpg|jpeg|png|webp)$')
  AND (metadata->>'owner')::uuid = auth.uid() -- ✅ Metadata check
);
```

#### **Read Policy with Dual Check:**
```sql
CREATE POLICY "driver_license_read_policy"
ON storage.objects
FOR SELECT
USING (
  bucket_id = 'driver-licenses' 
  AND auth.role() = 'authenticated'
  AND (
    name ~ ('^licenses/license_' || auth.uid()::text || '\.(jpg|jpeg|png|webp)$')
    OR (metadata->>'owner')::uuid = auth.uid() -- ✅ Metadata fallback
  )
);
```

### 3. Content Type Handling / معالجة نوع المحتوى

#### **Dynamic Content Type Detection:**
```dart
String contentType;
switch (fileExtension) {
  case 'jpg':
  case 'jpeg':
    contentType = 'image/jpeg';
    break;
  case 'png':
    contentType = 'image/png';
    break;
  case 'webp':
    contentType = 'image/webp';
    break;
  default:
    contentType = 'image/jpeg'; // Safe fallback
}
```

**Benefits:**
- ✅ **Proper MIME type** for each file format
- ✅ **Browser compatibility** for viewing
- ✅ **Storage optimization** by Supabase
- ✅ **Fallback handling** for unknown types

### 4. Enhanced Debug Logging / تسجيل تصحيح محسن

#### **Detailed Upload Information:**
```dart
if (kDebugMode) {
  print('📋 Content type: $contentType');
  print('👤 Owner metadata: ${currentUser.id}');
  print('📝 Upload options: ${uploadOptions.metadata}');
  print('📁 Upload path: $fullPath');
  print('🗂️ Bucket: $_driverLicensesBucket');
}
```

**Debug Output Example:**
```
✅ User authenticated: 550e8400-e29b-41d4-a716-************
📝 Requested userId: 550e8400-e29b-41d4-a716-************
📁 Upload path: licenses/license_550e8400-e29b-41d4-a716-************.jpg
📄 File extension: jpg
🗂️ Bucket: driver-licenses
📊 File size: 245760 bytes
📋 Content type: image/jpeg
👤 Owner metadata: 550e8400-e29b-41d4-a716-************
📝 Upload options: {owner: 550e8400-e29b-41d4-a716-************, uploaded_by: 550e8400-e29b-41d4-a716-************, file_type: driver_license, upload_timestamp: 2024-01-15T10:30:45.123Z}
🚀 Starting upload to Supabase...
✅ License uploaded successfully!
```

## File Structure and Metadata / هيكل الملف والبيانات الوصفية

### Correct File Structure / هيكل الملف الصحيح

```
driver-licenses/
└── licenses/
    └── license_550e8400-e29b-41d4-a716-************.jpg
        ├── Content-Type: image/jpeg
        └── Metadata:
            ├── owner: "550e8400-e29b-41d4-a716-************"
            ├── uploaded_by: "550e8400-e29b-41d4-a716-************"
            ├── file_type: "driver_license"
            └── upload_timestamp: "2024-01-15T10:30:45.123Z"
```

### Metadata Schema / مخطط البيانات الوصفية

```json
{
  "owner": "550e8400-e29b-41d4-a716-************",
  "uploaded_by": "550e8400-e29b-41d4-a716-************", 
  "file_type": "driver_license",
  "upload_timestamp": "2024-01-15T10:30:45.123Z"
}
```

**Field Descriptions:**
- **`owner`**: Required by RLS policy - must match `auth.uid()`
- **`uploaded_by`**: User who initiated the upload (same as owner)
- **`file_type`**: Identifies the document type for organization
- **`upload_timestamp`**: When the file was uploaded for audit trail

## Testing and Verification / الاختبار والتحقق

### 1. Pre-Upload Verification / التحقق قبل الرفع

```dart
// Verify user authentication
final currentUser = Supabase.instance.client.auth.currentUser;
print('Current user: ${currentUser?.id}');
print('Is authenticated: ${currentUser != null}');

// Verify file details
print('File name: ${imageFile.name}');
print('File extension: ${imageFile.name.split('.').last}');
```

### 2. Upload Process Testing / اختبار عملية الرفع

```dart
// Test the upload with debug logging enabled
final result = await StorageService.uploadDriverLicense(
  imageFile: selectedImage,
  userId: currentUser.id,
);

// Check result
if (result != null) {
  print('✅ Upload successful: $result');
} else {
  print('❌ Upload failed');
}
```

### 3. Post-Upload Verification / التحقق بعد الرفع

```sql
-- Check if file exists with correct metadata
SELECT 
  name, 
  metadata,
  content_type,
  created_at
FROM storage.objects 
WHERE bucket_id = 'driver-licenses' 
  AND name LIKE 'licenses/license_%'
ORDER BY created_at DESC;

-- Verify metadata owner field
SELECT 
  name,
  metadata->>'owner' as owner,
  metadata->>'file_type' as file_type
FROM storage.objects 
WHERE bucket_id = 'driver-licenses'
  AND name LIKE 'licenses/license_%';
```

### 4. Signed URL Testing / اختبار الرابط الموقع

```dart
// Test signed URL generation
final signedUrl = await StorageService.getDriverLicenseSignedUrl(currentUser.id);

if (signedUrl != null) {
  print('✅ Signed URL generated: $signedUrl');
} else {
  print('❌ Failed to generate signed URL');
}
```

## Expected Results / النتائج المتوقعة

### Successful Upload / رفع ناجح

**Console Output:**
```
✅ User authenticated: 550e8400-e29b-41d4-a716-************
📁 Upload path: licenses/license_550e8400-e29b-41d4-a716-************.jpg
📋 Content type: image/jpeg
👤 Owner metadata: 550e8400-e29b-41d4-a716-************
🚀 Starting upload to Supabase...
✅ License uploaded successfully!
📍 Path: licenses/license_550e8400-e29b-41d4-a716-************.jpg
```

**Database Verification:**
```sql
-- File should appear in storage.objects with correct metadata
name: licenses/license_550e8400-e29b-41d4-a716-************.jpg
metadata: {"owner": "550e8400-e29b-41d4-a716-************", ...}
content_type: image/jpeg
```

### Failed Upload (for comparison) / رفع فاشل (للمقارنة)

**Console Output:**
```
❌ Error uploading driver license: StorageException(...)
📋 Status code: 403
💬 Message: new row violates row-level security policy
⚠️ Error details: Unauthorized
```

## Implementation Steps / خطوات التطبيق

### Step 1: Update SQL Policies / تحديث سياسات SQL

1. **Execute** the updated `RESET_DRIVER_LICENSE_POLICIES.sql` script
2. **Verify** policies include metadata checks
3. **Test** policy functionality

### Step 2: Test Upload Flow / اختبار تدفق الرفع

1. **Enable debug mode** in Flutter app
2. **Attempt license upload** in driver activation
3. **Monitor console output** for detailed logging
4. **Verify success** or identify specific errors

### Step 3: Verify Results / التحقق من النتائج

1. **Check Supabase Storage** for uploaded files
2. **Verify metadata** is correctly set
3. **Test signed URL generation**
4. **Confirm security** with cross-user tests

## Security Benefits / فوائد الأمان

### Metadata-Based Security / الأمان القائم على البيانات الوصفية

- ✅ **Owner verification** through metadata
- ✅ **Audit trail** with upload timestamps
- ✅ **File type identification** for organization
- ✅ **User isolation** enforced by RLS

### Content Type Security / أمان نوع المحتوى

- ✅ **MIME type validation** prevents malicious files
- ✅ **File extension matching** ensures consistency
- ✅ **Browser safety** with proper content types
- ✅ **Storage optimization** by Supabase

## Conclusion / الخلاصة

The metadata upload fix provides:

- 🔧 **Proper metadata inclusion** with owner field for RLS compliance
- 📋 **Dynamic content type** detection for all image formats
- 🔍 **Enhanced debug logging** for troubleshooting
- 🛡️ **Improved security** with comprehensive metadata
- ✅ **Complete RLS policy compliance** for successful uploads

The upload should now work correctly with full security compliance and proper error handling.

إصلاح رفع البيانات الوصفية يوفر تضمين صحيح للبيانات الوصفية مع حقل المالك للامتثال لـ RLS واكتشاف ديناميكي لنوع المحتوى وتسجيل محسن للتصحيح وأمان محسن وامتثال كامل لسياسة RLS للرفع الناجح.
