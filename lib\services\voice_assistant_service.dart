import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:speech_to_text/speech_to_text.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:permission_handler/permission_handler.dart';

enum VoiceAssistantState {
  idle,
  listening,
  processing,
  speaking,
  error,
}

class VoiceAssistantService {
  static final VoiceAssistantService _instance =
      VoiceAssistantService._internal();
  factory VoiceAssistantService() => _instance;
  VoiceAssistantService._internal();

  final SpeechToText _speechToText = SpeechToText();
  final FlutterTts _flutterTts = FlutterTts();

  VoiceAssistantState _state = VoiceAssistantState.idle;
  String _lastRecognizedText = '';
  bool _isInitialized = false;

  // Stream controllers for state management
  final StreamController<VoiceAssistantState> _stateController =
      StreamController<VoiceAssistantState>.broadcast();
  final StreamController<String> _textController =
      StreamController<String>.broadcast();
  final StreamController<double> _soundLevelController =
      StreamController<double>.broadcast();

  // Getters
  VoiceAssistantState get state => _state;
  String get lastRecognizedText => _lastRecognizedText;
  bool get isInitialized => _isInitialized;

  // Streams
  Stream<VoiceAssistantState> get stateStream => _stateController.stream;
  Stream<String> get textStream => _textController.stream;
  Stream<double> get soundLevelStream => _soundLevelController.stream;

  /// Initialize the voice assistant service
  Future<bool> initialize() async {
    try {
      // Request microphone permission
      final micPermission = await Permission.microphone.request();
      if (micPermission != PermissionStatus.granted) {
        if (kDebugMode) {
          print('❌ Microphone permission denied');
        }
        return false;
      }

      // Initialize Speech-to-Text
      final sttAvailable = await _speechToText.initialize(
        onError: _onSpeechError,
        onStatus: _onSpeechStatus,
        debugLogging: kDebugMode,
      );

      if (!sttAvailable) {
        if (kDebugMode) {
          print('❌ Speech-to-Text not available');
        }
        return false;
      }

      // Initialize Text-to-Speech
      await _initializeTts();

      _isInitialized = true;
      _updateState(VoiceAssistantState.idle);

      if (kDebugMode) {
        print('✅ Voice Assistant Service initialized successfully');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Voice Assistant initialization error: $e');
      }
      _updateState(VoiceAssistantState.error);
      return false;
    }
  }

  /// Initialize Text-to-Speech with Arabic support and soft voice
  Future<void> _initializeTts() async {
    // Try Moroccan Arabic first, fallback to standard Arabic
    try {
      await _flutterTts.setLanguage('ar-MA'); // Moroccan Arabic
    } catch (e) {
      try {
        await _flutterTts.setLanguage('ar'); // Standard Arabic fallback
      } catch (e2) {
        await _flutterTts.setLanguage('en-US'); // English fallback
      }
    }

    // Configure for soft, friendly voice
    await _flutterTts.setSpeechRate(0.7); // Slower for warmth and clarity
    await _flutterTts.setVolume(0.9); // Slightly lower volume for softness
    await _flutterTts.setPitch(0.9); // Lower pitch for friendliness

    // Try to set a female voice if available (usually softer)
    try {
      final voices = await _flutterTts.getVoices;
      if (voices != null && voices.isNotEmpty) {
        // Look for Arabic female voices first
        final arabicFemaleVoices = voices
            .where((voice) =>
                voice['locale'].toString().startsWith('ar') &&
                voice['name'].toString().toLowerCase().contains('female'))
            .toList();

        if (arabicFemaleVoices.isNotEmpty) {
          await _flutterTts.setVoice(arabicFemaleVoices.first);
        } else {
          // Look for any Arabic voice
          final arabicVoices = voices
              .where((voice) => voice['locale'].toString().startsWith('ar'))
              .toList();

          if (arabicVoices.isNotEmpty) {
            await _flutterTts.setVoice(arabicVoices.first);
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Could not set specific voice: $e');
      }
    }

    // Set up TTS callbacks
    _flutterTts.setStartHandler(() {
      _updateState(VoiceAssistantState.speaking);
    });

    _flutterTts.setCompletionHandler(() {
      _updateState(VoiceAssistantState.idle);
    });

    _flutterTts.setErrorHandler((msg) {
      if (kDebugMode) {
        print('❌ TTS Error: $msg');
      }
      _updateState(VoiceAssistantState.error);
    });
  }

  /// Start listening for voice input
  Future<void> startListening() async {
    if (!_isInitialized || _state == VoiceAssistantState.listening) {
      return;
    }

    try {
      _updateState(VoiceAssistantState.listening);

      await _speechToText.listen(
        onResult: _onSpeechResult,
        listenFor: const Duration(seconds: 10),
        pauseFor: const Duration(seconds: 3),
        partialResults: true,
        localeId: 'ar-MA', // Moroccan Arabic
        onSoundLevelChange: (level) {
          _soundLevelController.add(level);
        },
      );

      if (kDebugMode) {
        print('🎤 Started listening...');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Start listening error: $e');
      }
      _updateState(VoiceAssistantState.error);
    }
  }

  /// Stop listening
  Future<void> stopListening() async {
    if (_state == VoiceAssistantState.listening) {
      await _speechToText.stop();
      _updateState(VoiceAssistantState.idle);

      if (kDebugMode) {
        print('🛑 Stopped listening');
      }
    }
  }

  /// Speak text using TTS
  Future<void> speak(String text) async {
    if (!_isInitialized || text.trim().isEmpty) {
      return;
    }

    try {
      // Stop any current speech
      await _flutterTts.stop();

      _updateState(VoiceAssistantState.speaking);
      await _flutterTts.speak(text);

      if (kDebugMode) {
        print('🔊 Speaking: $text');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Speak error: $e');
      }
      _updateState(VoiceAssistantState.error);
    }
  }

  /// Stop speaking
  Future<void> stopSpeaking() async {
    await _flutterTts.stop();
    if (_state == VoiceAssistantState.speaking) {
      _updateState(VoiceAssistantState.idle);
    }
  }

  /// Handle speech recognition results
  void _onSpeechResult(result) {
    final recognizedText = result.recognizedWords;
    _lastRecognizedText = recognizedText;
    _textController.add(recognizedText);

    if (kDebugMode) {
      print('🎯 Recognized: $recognizedText');
      print('📊 Confidence: ${result.confidence}');
    }

    if (result.finalResult) {
      _updateState(VoiceAssistantState.processing);
    }
  }

  /// Handle speech recognition errors
  void _onSpeechError(error) {
    if (kDebugMode) {
      print('❌ Speech error: $error');
    }
    _updateState(VoiceAssistantState.error);
  }

  /// Handle speech recognition status changes
  void _onSpeechStatus(status) {
    if (kDebugMode) {
      print('📱 Speech status: $status');
    }

    if (status == 'done' || status == 'notListening') {
      if (_state == VoiceAssistantState.listening) {
        _updateState(VoiceAssistantState.processing);
      }
    }
  }

  /// Update the current state
  void _updateState(VoiceAssistantState newState) {
    if (_state != newState) {
      _state = newState;
      _stateController.add(_state);

      if (kDebugMode) {
        print('🔄 State changed to: $_state');
      }
    }
  }

  /// Get available speech recognition locales
  Future<List<String>> getAvailableLocales() async {
    if (!_isInitialized) return [];

    final locales = await _speechToText.locales();
    return locales.map((locale) => locale.localeId).toList();
  }

  /// Check if speech recognition is available
  bool get isSpeechAvailable => _speechToText.isAvailable;

  /// Check if currently listening
  bool get isListening => _state == VoiceAssistantState.listening;

  /// Check if currently speaking
  bool get isSpeaking => _state == VoiceAssistantState.speaking;

  /// Dispose resources
  void dispose() {
    _speechToText.stop();
    _flutterTts.stop();
    _stateController.close();
    _textController.close();
    _soundLevelController.close();
  }
}
