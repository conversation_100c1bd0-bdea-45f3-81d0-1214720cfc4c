import 'dart:async';
import 'package:flutter/foundation.dart';
import 'supabase_service.dart';

class CitySearchService {
  static final CitySearchService _instance = CitySearchService._internal();
  factory CitySearchService() => _instance;
  CitySearchService._internal();

  // Cache for storing city results
  final Map<String, List<String>> _cache = {};
  final Map<String, DateTime> _cacheTimestamps = {};
  static const Duration _cacheExpiry = Duration(minutes: 10);

  // Debouncing
  Timer? _debounceTimer;
  static const Duration _debounceDuration = Duration(milliseconds: 300);

  // Current search state
  String? _lastQuery;
  List<String> _allCities = [];
  DateTime? _allCitiesTimestamp;

  /// Search for cities with debouncing and caching
  Future<List<String>> searchCities(String query) async {
    final completer = Completer<List<String>>();

    // Cancel previous timer
    _debounceTimer?.cancel();

    // Set up new timer
    _debounceTimer = Timer(_debounceDuration, () async {
      try {
        final results = await _performSearch(query);
        if (!completer.isCompleted) {
          completer.complete(results);
        }
      } catch (e) {
        if (!completer.isCompleted) {
          completer.completeError(e);
        }
      }
    });

    return completer.future;
  }

  /// Perform the actual search with caching
  Future<List<String>> _performSearch(String query) async {
    final normalizedQuery = query.trim().toLowerCase();

    // Check cache first
    if (_isCacheValid(normalizedQuery)) {
      if (kDebugMode) {
        print('🎯 City search cache hit for: "$normalizedQuery"');
      }
      return _cache[normalizedQuery]!;
    }

    try {
      List<String> results;

      if (normalizedQuery.isEmpty) {
        // Return all cities for empty query
        results = await _getAllCities();
      } else {
        // Search with query
        results = await _searchWithQuery(normalizedQuery);
      }

      // Cache the results
      _cache[normalizedQuery] = results;
      _cacheTimestamps[normalizedQuery] = DateTime.now();

      if (kDebugMode) {
        print(
            '🔍 City search completed for "$normalizedQuery": ${results.length} results');
      }

      return results;
    } catch (e) {
      if (kDebugMode) {
        print('❌ City search error for "$normalizedQuery": $e');
      }
      return [];
    }
  }

  /// Get all cities (cached)
  Future<List<String>> _getAllCities() async {
    // Check if we have cached all cities and they're still valid
    if (_allCities.isNotEmpty &&
        _allCitiesTimestamp != null &&
        DateTime.now().difference(_allCitiesTimestamp!) < _cacheExpiry) {
      return _allCities;
    }

    // Fetch all cities from Supabase
    _allCities = await SupabaseService.getDistinctCities();
    _allCitiesTimestamp = DateTime.now();

    return _allCities;
  }

  /// Search cities with a specific query
  Future<List<String>> _searchWithQuery(String query) async {
    // Get all cities first
    final allCities = await _getAllCities();

    // Filter locally for better performance
    final filteredCities = allCities.where((city) {
      final cityLower = city.toLowerCase();
      return cityLower.contains(query);
    }).toList();

    // Sort by relevance (exact matches first, then starts with, then contains)
    filteredCities.sort((a, b) {
      final aLower = a.toLowerCase();
      final bLower = b.toLowerCase();

      // Exact match
      if (aLower == query) return -1;
      if (bLower == query) return 1;

      // Starts with
      if (aLower.startsWith(query) && !bLower.startsWith(query)) return -1;
      if (bLower.startsWith(query) && !aLower.startsWith(query)) return 1;

      // Length preference for similar relevance (shorter names first)
      if (aLower.startsWith(query) && bLower.startsWith(query)) {
        return a.length.compareTo(b.length);
      }

      // Alphabetical for same relevance level
      return a.compareTo(b);
    });

    return filteredCities.take(8).toList(); // Limit to 8 results for better UX
  }

  /// Check if cache is valid for a query
  bool _isCacheValid(String query) {
    if (!_cache.containsKey(query) || !_cacheTimestamps.containsKey(query)) {
      return false;
    }

    final timestamp = _cacheTimestamps[query]!;
    return DateTime.now().difference(timestamp) < _cacheExpiry;
  }

  /// Clear all cache
  void clearCache() {
    _cache.clear();
    _cacheTimestamps.clear();
    _allCities.clear();
    _allCitiesTimestamp = null;

    if (kDebugMode) {
      print('🧹 City search cache cleared');
    }
  }

  /// Preload cities for better performance
  Future<void> preloadCities() async {
    try {
      await _getAllCities();
      if (kDebugMode) {
        print('🚀 Cities preloaded: ${_allCities.length} cities');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to preload cities: $e');
      }
    }
  }

  /// Get cache statistics for debugging
  Map<String, dynamic> getCacheStats() {
    return {
      'cached_queries': _cache.length,
      'all_cities_count': _allCities.length,
      'all_cities_cached': _allCitiesTimestamp != null,
      'cache_expiry_minutes': _cacheExpiry.inMinutes,
    };
  }

  /// Dispose resources
  void dispose() {
    _debounceTimer?.cancel();
    clearCache();
  }
}
