# Supabase Auth Implementation Guide

## 🎯 Overview
This implementation uses **Supabase's built-in authentication** with phone numbers and passwords, exactly as requested. The system:
- Uses `Supabase.auth.signUp()` and `signInWithPassword()` with phone numbers
- Stores user profiles in a custom `users` table linked to `auth.users`
- Handles phone number formatting (+212 prefix)
- Works with Flutter Web and avoids layout issues

## 📋 Implementation Steps

### 1. Database Migration
Run the `supabase_auth_migration.sql` script in your Supabase SQL Editor:

```sql
-- This will:
-- 1. Drop the old custom users table
-- 2. Create a new users table linked to auth.users
-- 3. Add RLS policies
-- 4. Create trigger for automatic profile creation
```

### 2. Enable Phone Authentication in Supabase
1. Go to your Supabase project dashboard
2. Navigate to **Authentication > Settings**
3. Enable **Phone** authentication
4. For development, you can use the built-in phone provider
5. For production, configure <PERSON><PERSON><PERSON> or another SMS provider

### 3. Key Changes Made

#### A. SupabaseService (`lib/services/supabase_service.dart`)
- ✅ **Uses Supabase Auth** - `signUp()` and `signInWithPassword()`
- ✅ **Phone formatting** - Converts to +212XXXXXXXXX format
- ✅ **Proper error handling** - Arabic error messages
- ✅ **Auth state management** - Uses Supabase's built-in session handling

#### B. AuthProvider (`lib/providers/auth_provider.dart`)
- ✅ **Auth state listener** - Responds to Supabase auth changes
- ✅ **Automatic profile loading** - Fetches user data after login
- ✅ **Session persistence** - Handled by Supabase automatically

#### C. Database Schema
- ✅ **Users table** - Links to `auth.users` with foreign key
- ✅ **Automatic profile creation** - Trigger creates profile on signup
- ✅ **RLS policies** - Secure access to user data

## 🧪 Testing Instructions

### Step 1: Apply Database Changes
1. Run `supabase_auth_migration.sql` in Supabase SQL Editor
2. Enable phone authentication in Supabase dashboard

### Step 2: Test Signup Flow
1. Run the app: `flutter run -d chrome`
2. Navigate to signup page
3. Create account with:
   - Full Name: "تجربة مستخدم"
   - Phone: "**********" (will be converted to +************)
   - Password: "123456"
4. ✅ Should create account and redirect to home page

### Step 3: Test Login Flow
1. Use the account created in Step 2
2. Login with:
   - Phone: "**********" or "*********"
   - Password: "123456"
3. ✅ Should login successfully and redirect to home page

### Step 4: Test Session Persistence
1. Login successfully
2. Close and reopen the app
3. ✅ Should automatically redirect to home page (session persisted)
4. Logout and reopen app
5. ✅ Should redirect to login page

## 🔧 Key Features

### Phone Number Handling
```dart
// Input formats supported:
"**********"     → "+************"
"*********"      → "+************"
"+************"  → "+************"
"************"   → "+************"
```

### Authentication Flow
```dart
// Signup
final response = await Supabase.instance.client.auth.signUp(
  phone: '+************',
  password: 'password',
  data: {
    'full_name': 'User Name',
    'phone': '*********', // normalized for database
  },
);

// Login
final response = await Supabase.instance.client.auth.signInWithPassword(
  phone: '+************',
  password: 'password',
);

// Get user profile
final userId = Supabase.instance.client.auth.currentUser?.id;
final profile = await Supabase.instance.client
  .from('users')
  .select()
  .eq('id', userId)
  .single();
```

### Error Handling
- ✅ **Arabic error messages** for all scenarios
- ✅ **Network error handling** with user-friendly feedback
- ✅ **Validation errors** for invalid phone numbers/passwords

## 🚀 Production Considerations

### Phone Provider Setup
For production, configure a phone provider in Supabase:
1. Go to Authentication > Settings > Phone
2. Choose provider (Twilio recommended)
3. Add API credentials
4. Configure SMS templates

### Security
- ✅ **RLS policies** protect user data
- ✅ **Supabase Auth** handles password hashing
- ✅ **Session management** handled securely by Supabase

## 🐛 Troubleshooting

### Login Failures
1. **"Invalid login credentials"**
   - Check phone number format (+212 prefix)
   - Verify password is correct
   - Ensure user exists in auth.users table

2. **"Phone not confirmed"**
   - For development, phone confirmation is usually disabled
   - Check Supabase Auth settings

3. **Network errors**
   - Check internet connection
   - Verify Supabase project URL and keys

### Flutter Web Issues
- ✅ **Fixed**: Proper context handling in async operations
- ✅ **Fixed**: Auth state management works on web
- ✅ **Fixed**: No layout overflow issues

## 📱 Testing Commands

```bash
# Run on Chrome
flutter run -d chrome

# Clean and rebuild if needed
flutter clean
flutter pub get
flutter run -d chrome

# Check for issues
flutter analyze
```

## ✅ **Verification Checklist**

- [ ] Database migration applied successfully
- [ ] Phone authentication enabled in Supabase
- [ ] Signup creates user in both auth.users and users tables
- [ ] Login works with phone + password
- [ ] Session persists across app restarts
- [ ] Error messages display in Arabic
- [ ] No runtime errors in Flutter Web
- [ ] Navigation works correctly after login/logout

The authentication system now uses Supabase's built-in auth as requested! 🎉
