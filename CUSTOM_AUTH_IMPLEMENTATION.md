# Custom Authentication Implementation Guide

## 🎯 Overview
This guide implements a custom authentication system for the Safarni Flutter app using:
- **Custom users table** (no Supabase Auth dependency)
- **Phone number + password** authentication
- **Local session management** with SharedPreferences
- **Direct database queries** for login/signup

## 📋 Implementation Steps

### 1. Database Migration
Run the `custom_auth_migration.sql` script in your Supabase SQL editor:

```sql
-- This will:
-- 1. Drop the old auth-dependent users table
-- 2. Create a new standalone users table
-- 3. Add RLS policies for public access
-- 4. Insert test users for development
```

### 2. Key Changes Made

#### A. SupabaseService (`lib/services/supabase_service.dart`)
- ✅ **Removed Supabase Auth dependency**
- ✅ **Added custom signUpWithPhone()** - Creates users directly in custom table
- ✅ **Added custom signInWithPhone()** - Queries users table and validates password
- ✅ **Added session management** with SharedPreferences
- ✅ **Added phone normalization** for consistent formatting

#### B. AuthProvider (`lib/providers/auth_provider.dart`)
- ✅ **Updated initialization** to use custom session management
- ✅ **Removed Supabase Auth listeners**
- ✅ **Maintained existing API** for seamless integration

#### C. Main App (`lib/main.dart`)
- ✅ **Fixed routing** to start with SplashPage
- ✅ **Maintained RTL support** and theming

#### D. Splash Page (`lib/pages/splash_page.dart`)
- ✅ **Updated auth checking** to use custom session management
- ✅ **Fixed context usage** across async gaps

## 🧪 Testing Instructions

### Step 1: Apply Database Changes
1. Open your Supabase project dashboard
2. Go to SQL Editor
3. Run the `custom_auth_migration.sql` script
4. Verify the new users table is created with test data

### Step 2: Test Signup Flow
1. Run the app: `flutter run -d chrome`
2. Navigate to the signup page
3. Try creating a new account with:
   - Full Name: "تجربة مستخدم"
   - Phone: "*********"
   - Password: "123456"
4. ✅ Should create account and redirect to home page

### Step 3: Test Login Flow
1. Use existing test accounts:
   - Phone: "*********", Password: "123456" (أحمد محمد)
   - Phone: "*********", Password: "123456" (فاطمة علي)
2. ✅ Should login successfully and redirect to home page

### Step 4: Test Session Management
1. Login successfully
2. Close and reopen the app
3. ✅ Should automatically redirect to home page (session persisted)
4. Logout and reopen app
5. ✅ Should redirect to login page

### Step 5: Test Error Handling
1. Try login with wrong password
2. ✅ Should show Arabic error message
3. Try login with unregistered phone
4. ✅ Should show "رقم الهاتف غير مسجل" message

## 🔧 Key Features

### Phone Number Normalization
- Removes non-digit characters
- Handles +212 and 0 prefixes
- Consistent storage format

### Session Management
- Uses SharedPreferences for local storage
- Stores user ID and login status
- Automatic session restoration on app restart

### Error Handling
- Arabic error messages
- Graceful handling of network issues
- User-friendly feedback

### Security Notes
- ⚠️ **Passwords are currently plain text** (will be hashed later)
- ✅ **RLS policies** allow necessary operations
- ✅ **Input validation** for phone numbers and passwords

## 🚀 Next Steps

1. **Password Hashing**: Implement bcrypt or similar for password security
2. **Phone Validation**: Add proper phone number format validation
3. **Email Verification**: Optional email verification feature
4. **Rate Limiting**: Implement login attempt limits
5. **Password Reset**: Add password reset functionality

## 🐛 Troubleshooting

### White Screen Issue
- ✅ **Fixed**: App now starts with SplashPage that checks authentication
- ✅ **Fixed**: Proper context handling in async operations

### Login Failures
- Check database connection
- Verify RLS policies are applied
- Check phone number normalization
- Verify test users exist in database

### Session Issues
- Clear app data if needed: `flutter clean`
- Check SharedPreferences permissions
- Verify session save/load logic

## 📱 Testing on Flutter Web

```bash
# Run on Chrome
flutter run -d chrome

# Build for web
flutter build web

# Serve locally
cd build/web && python -m http.server 8000
```

The custom authentication system is now ready for testing! 🎉
