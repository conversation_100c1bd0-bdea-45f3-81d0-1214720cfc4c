import 'package:flutter/material.dart';
import '../../constants/app_theme.dart';
import '../../controllers/voice_assistant_controller.dart';
import '../../services/voice_assistant_service.dart';
import '../../widgets/voice_assistant_widget.dart';
import '../../widgets/trip_card.dart';
import '../../models/trip_model.dart';
import '../trip/trip_details_page.dart';
import '../../utils/navigation_utils.dart';

class VoiceAssistantPage extends StatefulWidget {
  const VoiceAssistantPage({super.key});

  @override
  State<VoiceAssistantPage> createState() => _VoiceAssistantPageState();
}

class _VoiceAssistantPageState extends State<VoiceAssistantPage> {
  final VoiceAssistantController _controller = VoiceAssistantController();

  VoiceAssistantState _currentState = VoiceAssistantState.idle;
  double _soundLevel = 0.0;
  String _recognizedText = '';
  String _statusMessage = '';
  List<TripModel> _foundTrips = [];
  bool _isSearching = false;
  List<String> _recentCityPairs = [];

  @override
  void initState() {
    super.initState();
    _initializeVoiceAssistant();
  }

  Future<void> _initializeVoiceAssistant() async {
    final initialized = await _controller.initialize();
    if (!initialized) {
      setState(() {
        _currentState = VoiceAssistantState.error;
        _statusMessage = 'فشل في تهيئة المساعد الصوتي';
      });
      return;
    }

    // Listen to controller streams
    _controller.stateStream.listen((state) {
      setState(() {
        _currentState = state;
      });
    });

    _controller.textStream.listen((text) {
      setState(() {
        _recognizedText = text;
      });
    });

    _controller.soundLevelStream.listen((level) {
      setState(() {
        _soundLevel = level;
      });
    });

    _controller.statusStream.listen((status) {
      setState(() {
        _statusMessage = status;
      });
    });

    _controller.tripsStream.listen((trips) {
      setState(() {
        _foundTrips = trips;
      });
    });

    _controller.searchingStream.listen((searching) {
      setState(() {
        _isSearching = searching;
      });
    });

    // Load recent city pairs
    setState(() {
      _recentCityPairs = _controller.recentCityPairs;
    });
  }

  void _onAssistantTap() async {
    await _controller.onAssistantTap();
  }

  void _retrySearch() async {
    await _controller.retrySearch();
  }

  void _onCityPairTap(String cityPair) async {
    // Parse city pair like "الدار البيضاء ⇌ الرباط"
    final parts = cityPair.split(' ⇌ ');
    if (parts.length == 2) {
      await _controller.searchCityPair(parts[0].trim(), parts[1].trim());
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenHeight = MediaQuery.of(context).size.height;
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('المساعد الصوتي'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          physics: const ClampingScrollPhysics(),
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight: screenHeight -
                  MediaQuery.of(context).padding.top -
                  kToolbarHeight -
                  keyboardHeight,
            ),
            child: IntrinsicHeight(
              child: Column(
                children: [
                  // Voice Assistant Section
                  Container(
                    width: double.infinity,
                    constraints: BoxConstraints(
                      minHeight: (screenHeight * 0.4).clamp(300.0, 500.0),
                    ),
                    decoration: const BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [AppColors.primary, AppColors.background],
                        stops: [0.0, 0.7],
                      ),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 20),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // Voice Assistant Widget
                          VoiceAssistantWidget(
                            state: _currentState,
                            soundLevel: _soundLevel,
                            onTap: _onAssistantTap,
                            size: 180, // Slightly smaller for better fit
                          ),

                          const SizedBox(height: 20),

                          // Status Text
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 24),
                            child: VoiceAssistantStatusText(
                              state: _currentState,
                              customText: _statusMessage.isNotEmpty
                                  ? _statusMessage
                                  : null,
                            ),
                          ),

                          const SizedBox(height: 12),

                          // Recognized Text
                          if (_recognizedText.isNotEmpty)
                            Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 24),
                              child: Container(
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: Colors.white.withValues(alpha: 0.9),
                                  borderRadius: BorderRadius.circular(12),
                                  boxShadow: [
                                    BoxShadow(
                                      color:
                                          Colors.black.withValues(alpha: 0.1),
                                      blurRadius: 8,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: Text(
                                  _recognizedText,
                                  style: theme.textTheme.bodyMedium?.copyWith(
                                    color: AppColors.textPrimary,
                                  ),
                                  textAlign: TextAlign.center,
                                  maxLines: 3,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),

                  // Results Section
                  Expanded(
                    child: Container(
                      width: double.infinity,
                      constraints: BoxConstraints(
                        minHeight:
                            (screenHeight * 0.5).clamp(200.0, double.infinity),
                      ),
                      decoration: const BoxDecoration(
                        color: AppColors.background,
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(24),
                          topRight: Radius.circular(24),
                        ),
                      ),
                      child: _buildResultsSection(),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildResultsSection() {
    if (_isSearching) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text(
              'جاري البحث عن الرحلات...',
              style: TextStyle(
                fontSize: 16,
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      );
    }

    if (_foundTrips.isEmpty && _controller.lastQuery?.isValid == true) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.search_off,
              size: 64,
              color: AppColors.textTertiary,
            ),
            const SizedBox(height: 16),
            const Text(
              'لم يتم العثور على رحلات',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'لا توجد رحلات متاحة من ${_controller.lastQuery?.fromCity} إلى ${_controller.lastQuery?.toCity}',
              style: const TextStyle(
                fontSize: 14,
                color: AppColors.textTertiary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _retrySearch,
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة البحث'),
            ),
          ],
        ),
      );
    }

    if (_foundTrips.isNotEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              'الرحلات المتاحة (${_foundTrips.length})',
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
          ),
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: _foundTrips.length,
              itemBuilder: (context, index) {
                final trip = _foundTrips[index];
                return TripCard(
                  trip: trip,
                  onTap: () {
                    // Navigate directly to trip details page
                    _navigateToTripDetails(context, trip);
                  },
                );
              },
            ),
          ),
        ],
      );
    }

    // Default state - show instructions and recent searches
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.mic,
              size: 64,
              color: AppColors.primary,
            ),
            const SizedBox(height: 16),
            const Text(
              'مرحباً بك في المساعد الصوتي',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            const Text(
              'اضغط على الدائرة وقل شيئاً مثل:\n"بغيت نمشي من الدار البيضاء ل الرباط"',
              style: TextStyle(
                fontSize: 14,
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),

            // Recent city searches
            if (_recentCityPairs.isNotEmpty) ...[
              const SizedBox(height: 32),
              const Text(
                'آخر الرحلات:',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
              const SizedBox(height: 12),
              ...(_recentCityPairs.take(3).map(
                    (cityPair) => Padding(
                      padding: const EdgeInsets.symmetric(vertical: 4),
                      child: InkWell(
                        onTap: () => _onCityPairTap(cityPair),
                        borderRadius: BorderRadius.circular(8),
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 8,
                          ),
                          decoration: BoxDecoration(
                            color: AppColors.primary.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: AppColors.primary.withValues(alpha: 0.3),
                            ),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(
                                Icons.history,
                                size: 16,
                                color: AppColors.primary,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                cityPair,
                                style: const TextStyle(
                                  color: AppColors.primary,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  )),
            ],
          ],
        ),
      ),
    );
  }

  /// Navigate to trip details page with null safety
  void _navigateToTripDetails(BuildContext context, TripModel trip) {
    // Ensure trip has a valid ID before navigation
    if (trip.id.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('خطأ: معرف الرحلة غير صحيح'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    try {
      NavigationUtils.pushWithTransition(
        context,
        TripDetailsPage(tripId: trip.id),
        type: TransitionType.slide,
        duration: const Duration(milliseconds: 400),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('خطأ في التنقل إلى تفاصيل الرحلة'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
