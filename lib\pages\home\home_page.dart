import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_theme.dart';
import '../../constants/app_constants.dart';
import '../../providers/auth_provider.dart';
import '../../providers/trip_provider.dart';
import '../../widgets/trip_card.dart';
import '../../widgets/custom_button.dart';
import '../../widgets/custom_text_field.dart';
import '../../widgets/city_autocomplete_search.dart';
import '../profile/profile_page.dart';
import '../trip_leader/trip_leader_dashboard.dart';
import '../trip/trip_details_page.dart';
import '../voice_assistant/voice_assistant_demo.dart';
import '../../models/trip_model.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  final _searchController = TextEditingController();
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadInitialData();
    });
  }

  void _loadInitialData() {
    final tripProvider = Provider.of<TripProvider>(context, listen: false);
    tripProvider.loadTrips(refresh: true);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        // If user is a trip leader, redirect to trip leader dashboard
        if (authProvider.currentUser?.isLeader == true) {
          return const TripLeaderDashboard();
        }

        // Show traveler interface
        return Scaffold(
          body: IndexedStack(
            index: _currentIndex,
            children: [
              const _HomeTab(),
              const _SearchTab(),
              const _TripsTab(),
              const _MessagesTab(),
              const ProfilePage(),
            ],
          ),
          bottomNavigationBar: _buildBottomNavigationBar(),
          floatingActionButton: _buildFloatingActionButton(),
        );
      },
    );
  }

  Widget _buildBottomNavigationBar() {
    return BottomNavigationBar(
      currentIndex: _currentIndex,
      onTap: (index) {
        setState(() {
          _currentIndex = index;
        });
      },
      type: BottomNavigationBarType.fixed,
      selectedItemColor: AppColors.primary,
      unselectedItemColor: AppColors.textTertiary,
      items: [
        BottomNavigationBarItem(
          icon: const Icon(Icons.home),
          label: 'الرئيسية',
        ),
        BottomNavigationBarItem(
          icon: const Icon(Icons.search),
          label: 'البحث',
        ),
        BottomNavigationBarItem(
          icon: const Icon(Icons.directions_car),
          label: 'رحلاتي',
        ),
        BottomNavigationBarItem(
          icon: const Icon(Icons.message),
          label: 'الرسائل',
        ),
        BottomNavigationBarItem(
          icon: const Icon(Icons.person),
          label: 'الملف الشخصي',
        ),
      ],
    );
  }

  Widget? _buildFloatingActionButton() {
    final authProvider = Provider.of<AuthProvider>(context);

    if (_currentIndex == 2 && authProvider.isTripLeader) {
      return FloatingActionButton(
        onPressed: () {
          // TODO: Navigate to create trip
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('إنشاء رحلة - قيد التطوير')),
          );
        },
        tooltip: 'إنشاء رحلة',
        child: const Icon(Icons.add),
      );
    }

    return null;
  }
}

class _HomeTab extends StatelessWidget {
  const _HomeTab();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final authProvider = Provider.of<AuthProvider>(context);

    return CustomScrollView(
      slivers: [
        // App Bar
        SliverAppBar(
          expandedHeight: 200,
          floating: false,
          pinned: true,
          backgroundColor: AppColors.primary,
          flexibleSpace: FlexibleSpaceBar(
            title: const Text(
              'سفرني',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            background: Container(
              decoration: const BoxDecoration(
                gradient: AppColors.primaryGradient,
              ),
              child: SafeArea(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 20),
                      Text(
                        'مرحباً ${authProvider.currentUser?.fullName ?? ''}',
                        style: theme.textTheme.headlineMedium?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'سافر جماعة، بتمن مناسب، بأمان تام!',
                        style: theme.textTheme.bodyLarge?.copyWith(
                          color: Colors.white.withValues(alpha: 0.9),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
          actions: [
            IconButton(
              icon: const Icon(Icons.mic, color: Colors.white),
              onPressed: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const VoiceAssistantDemo(),
                  ),
                );
              },
            ),
            IconButton(
              icon: const Icon(Icons.notifications, color: Colors.white),
              onPressed: () {
                // TODO: Navigate to notifications
              },
            ),
          ],
        ),

        // Quick Actions
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'إجراءات سريعة',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: _QuickActionCard(
                        icon: Icons.search,
                        title: 'البحث عن رحلة',
                        subtitle: 'ابحث عن رحلتك المثالية',
                        onTap: () {
                          _showSearchDialog(context);
                        },
                      ),
                    ),
                    const SizedBox(width: 12),
                    if (authProvider.isTripLeader)
                      Expanded(
                        child: _QuickActionCard(
                          icon: Icons.add_circle,
                          title: 'إنشاء رحلة',
                          subtitle: 'أنشئ رحلة جديدة',
                          onTap: () {
                            // TODO: Navigate to create trip
                          },
                        ),
                      )
                    else
                      Expanded(
                        child: _QuickActionCard(
                          icon: Icons.bookmark,
                          title: 'حجوزاتي',
                          subtitle: 'تابع حجوزاتك',
                          onTap: () {
                            // TODO: Navigate to traveler dashboard
                          },
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ),
        ),

        // Search Section
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'بحث عن رحلة',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 12),
                Consumer<TripProvider>(
                  builder: (context, tripProvider, child) {
                    return CityAutocompleteSearch(
                      hintText: 'ابحث عن مدينة...',
                      onCitySelected: (city) {
                        if (city.isEmpty) {
                          // Clear filter and show all trips
                          tripProvider.clearFilters();
                          tripProvider.loadTrips(refresh: true);
                        } else {
                          // Filter trips by selected city (either from or to)
                          tripProvider.setSearchCity(city);
                          tripProvider.applyFilters();
                        }
                      },
                      onSearchChanged: (query) {
                        // Optional: Real-time filtering as user types
                        // Can be implemented later if needed
                      },
                    );
                  },
                ),
              ],
            ),
          ),
        ),

        // Recent Trips
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'الرحلات الحديثة',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    // TODO: Navigate to search
                  },
                  child: const Text('عرض الكل'),
                ),
              ],
            ),
          ),
        ),

        // Trips List
        Consumer<TripProvider>(
          builder: (context, tripProvider, child) {
            if (tripProvider.isLoading) {
              return const SliverToBoxAdapter(
                child: Center(
                  child: Padding(
                    padding: EdgeInsets.all(32),
                    child: CircularProgressIndicator(),
                  ),
                ),
              );
            }

            if (tripProvider.trips.isEmpty) {
              return SliverToBoxAdapter(
                child: Center(
                  child: Padding(
                    padding: const EdgeInsets.all(32),
                    child: Column(
                      children: [
                        const Icon(
                          Icons.directions_car_outlined,
                          size: 64,
                          color: AppColors.textTertiary,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'لا توجد رحلات متاحة حالياً',
                          style: theme.textTheme.titleMedium?.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'تحقق مرة أخرى لاحقاً أو ابحث عن رحلات في مدن أخرى',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: AppColors.textTertiary,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }

            return SliverList(
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  if (index >= tripProvider.trips.length) return null;
                  return TripCard(
                    trip: tripProvider.trips[index],
                    onTap: () =>
                        _showTripDetails(context, tripProvider.trips[index]),
                  );
                },
                childCount: tripProvider.trips.length.clamp(0, 5),
              ),
            );
          },
        ),

        // Bottom padding
        const SliverToBoxAdapter(
          child: SizedBox(height: 100),
        ),
      ],
    );
  }
}

class _QuickActionCard extends StatelessWidget {
  final IconData icon;
  final String title;
  final String subtitle;
  final VoidCallback onTap;

  const _QuickActionCard({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  icon,
                  color: AppColors.primary,
                  size: 24,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: AppColors.textSecondary,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// Placeholder tabs
class _SearchTab extends StatelessWidget {
  const _SearchTab();

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text('صفحة البحث - قيد التطوير'),
    );
  }
}

class _TripsTab extends StatelessWidget {
  const _TripsTab();

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text('صفحة الرحلات - قيد التطوير'),
    );
  }
}

class _MessagesTab extends StatelessWidget {
  const _MessagesTab();

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text('صفحة الرسائل - قيد التطوير'),
    );
  }
}

// Helper functions
void _showSearchDialog(BuildContext context) {
  showDialog(
    context: context,
    builder: (context) => AlertDialog(
      title: const Text('البحث عن رحلة'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          CustomTextField(
            label: 'من',
            prefixIcon: Icons.location_on,
          ),
          const SizedBox(height: 16),
          CustomTextField(
            label: 'إلى',
            prefixIcon: Icons.location_on,
          ),
          const SizedBox(height: 16),
          CustomTextField(
            label: 'التاريخ',
            prefixIcon: Icons.calendar_today,
            readOnly: true,
            onTap: () async {
              final date = await showDatePicker(
                context: context,
                initialDate: DateTime.now(),
                firstDate: DateTime.now(),
                lastDate: DateTime.now().add(const Duration(days: 365)),
              );
              // Handle date selection
            },
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: () {
            Navigator.pop(context);
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('البحث - قيد التطوير'),
              ),
            );
          },
          child: const Text('بحث'),
        ),
      ],
    ),
  );
}

void _showTripDetails(BuildContext context, TripModel trip) {
  Navigator.push(
    context,
    MaterialPageRoute(
      builder: (context) => TripDetailsPage(tripId: trip.id),
    ),
  );
}
