import 'dart:io';
import 'dart:typed_data';
import 'dart:ui';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../constants/app_theme.dart';
import '../../providers/auth_provider.dart';
import '../../services/storage_service.dart';
import '../../services/supabase_service.dart';
import '../../widgets/slide_to_confirm_button.dart';
import '../home/<USER>';

class ProfileImageUploadPage extends StatefulWidget {
  const ProfileImageUploadPage({super.key});

  @override
  State<ProfileImageUploadPage> createState() => _ProfileImageUploadPageState();
}

class _ProfileImageUploadPageState extends State<ProfileImageUploadPage>
    with TickerProviderStateMixin {
  XFile? _selectedImage;
  bool _isUploading = false;
  bool _isImageLoading = false;
  String? _imageError;
  late AnimationController _fadeController;
  late AnimationController _scaleController;
  late AnimationController _pulseController;
  late AnimationController _borderController;
  late AnimationController _celebrationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _pulseAnimation;
  late Animation<double> _borderAnimation;
  late Animation<double> _celebrationAnimation;

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    _borderController = AnimationController(
      duration: const Duration(milliseconds: 3000),
      vsync: this,
    );
    _celebrationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );
    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _scaleController, curve: Curves.elasticOut),
    );
    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.05).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );
    _borderAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _borderController, curve: Curves.easeInOut),
    );
    _celebrationAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _celebrationController, curve: Curves.elasticOut),
    );

    _fadeController.forward();
    _scaleController.forward();
    _pulseController.repeat(reverse: true);
    _borderController.repeat();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _scaleController.dispose();
    _pulseController.dispose();
    _borderController.dispose();
    _celebrationController.dispose();
    super.dispose();
  }

  Future<void> _pickImage(ImageSource source) async {
    setState(() {
      _isImageLoading = true;
      _imageError = null;
    });

    try {
      XFile? pickedFile;

      if (kIsWeb) {
        // For web, use FilePicker with image type (no custom extensions needed)
        FilePickerResult? result = await FilePicker.platform.pickFiles(
          type: FileType.image,
          allowMultiple: false,
        );

        if (result != null && result.files.single.bytes != null) {
          final file = result.files.single;

          // Validate file type
          final fileName = file.name.toLowerCase();
          final validExtensions = ['jpg', 'jpeg', 'png', 'webp', 'gif'];
          final hasValidExtension = validExtensions.any((ext) => fileName.endsWith('.$ext'));

          if (!hasValidExtension) {
            throw Exception('نوع الملف غير مدعوم. يرجى اختيار صورة بصيغة JPG, PNG, أو WEBP');
          }

          // Check file size (max 5MB)
          if (file.size > 5 * 1024 * 1024) {
            throw Exception('حجم الصورة كبير جداً. يرجى اختيار صورة أصغر من 5 ميجابايت');
          }

          pickedFile = XFile.fromData(
            file.bytes!,
            name: file.name,
            mimeType: _getMimeType(fileName),
          );
        }
      } else {
        // For mobile, use ImagePicker
        pickedFile = await ImagePicker().pickImage(
          source: source,
          maxWidth: 1024,
          maxHeight: 1024,
          imageQuality: 90,
        );

        // Check file size for mobile
        if (pickedFile != null) {
          final bytes = await pickedFile.readAsBytes();
          if (bytes.length > 5 * 1024 * 1024) {
            throw Exception('حجم الصورة كبير جداً. يرجى اختيار صورة أصغر من 5 ميجابايت');
          }
        }
      }

      if (pickedFile != null) {
        if (kDebugMode) {
          print('✅ Image selected successfully: ${pickedFile.name}');
          print('📏 File size: ${(await pickedFile.readAsBytes()).length} bytes');
          print('🎯 MIME type: ${pickedFile.mimeType ?? 'unknown'}');
        }

        setState(() {
          _selectedImage = pickedFile;
          _isImageLoading = false;
        });
        HapticFeedback.mediumImpact();

        // Show success feedback
        _showSuccessSnackBar('تم اختيار الصورة بنجاح!');
      } else {
        setState(() {
          _isImageLoading = false;
        });

        if (kDebugMode) {
          print('❌ No image selected');
        }
      }
    } catch (e) {
      setState(() {
        _isImageLoading = false;
        _imageError = e.toString();
      });

      if (kDebugMode) {
        print('Error picking image: $e');
      }

      String errorMessage = 'فشل في اختيار الصورة';
      if (e.toString().contains('حجم الصورة')) {
        errorMessage = e.toString().replaceAll('Exception: ', '');
      }

      _showErrorSnackBar(errorMessage);
    }
  }

  String _getMimeType(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();
    switch (extension) {
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'webp':
        return 'image/webp';
      case 'gif':
        return 'image/gif';
      default:
        return 'image/jpeg'; // Default fallback
    }
  }

  void _showImageSourceDialog() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(height: 20),
              const Text(
                'اختر مصدر الصورة',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 20),
              // Show different options based on platform
              if (kIsWeb) ...[
                // Web only shows file picker
                _buildSourceOption(
                  icon: Icons.photo_library,
                  label: 'اختر من الجهاز',
                  onTap: () {
                    Navigator.pop(context);
                    _pickImage(ImageSource.gallery); // Will use FilePicker on web
                  },
                ),
              ] else ...[
                // Mobile shows both camera and gallery
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildSourceOption(
                      icon: Icons.camera_alt,
                      label: 'الكاميرا',
                      onTap: () {
                        Navigator.pop(context);
                        _pickImage(ImageSource.camera);
                      },
                    ),
                    _buildSourceOption(
                      icon: Icons.photo_library,
                      label: 'المعرض',
                      onTap: () {
                        Navigator.pop(context);
                        _pickImage(ImageSource.gallery);
                      },
                    ),
                  ],
                ),
              ],
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSourceOption({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 100,
        padding: const EdgeInsets.symmetric(vertical: 20),
        decoration: BoxDecoration(
          color: AppColors.primary.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            Icon(icon, size: 30, color: AppColors.primary),
            const SizedBox(height: 8),
            Text(label, style: const TextStyle(fontWeight: FontWeight.w600)),
          ],
        ),
      ),
    );
  }

  Future<void> _uploadAndContinue() async {
    if (_selectedImage == null) {
      _showErrorSnackBar('يرجى اختيار صورة شخصية أولاً');
      return;
    }

    setState(() => _isUploading = true);

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final currentUser = authProvider.currentUser;

      if (currentUser == null) {
        _showErrorSnackBar('خطأ في المصادقة. يرجى تسجيل الدخول مرة أخرى');
        return;
      }

      // Upload image to Supabase Storage with retry mechanism
      String? imageUrl;
      int retryCount = 0;
      const maxRetries = 3;

      while (retryCount < maxRetries && imageUrl == null) {
        try {
          if (kDebugMode) {
            print('🔄 Upload attempt ${retryCount + 1}/$maxRetries');
            print('📁 File: ${_selectedImage!.name}');
            print('👤 User ID: ${currentUser.id}');
          }

          imageUrl = await StorageService.uploadUserProfileImage(
            imageFile: _selectedImage!,
            userId: currentUser.id,
          );

          if (imageUrl != null) {
            if (kDebugMode) {
              print('✅ Upload successful: $imageUrl');
            }
          } else {
            retryCount++;
            if (kDebugMode) {
              print('❌ Upload failed, attempt $retryCount/$maxRetries');
            }
            if (retryCount < maxRetries) {
              await Future.delayed(Duration(seconds: retryCount * 2));
            }
          }
        } catch (uploadError) {
          retryCount++;
          if (kDebugMode) {
            print('💥 Upload error (attempt $retryCount/$maxRetries): $uploadError');
          }
          if (retryCount >= maxRetries) {
            rethrow;
          }
          await Future.delayed(Duration(seconds: retryCount * 2));
        }
      }

      if (imageUrl != null) {
        // Update the auth provider with new user data
        final updatedUser = currentUser.copyWith(
          profileImageUrl: imageUrl,
          updatedAt: DateTime.now(),
        );

        // Update user profile in database
        await SupabaseService.updateUserProfile(updatedUser);

        // Update the auth provider
        authProvider.setCurrentUser(updatedUser);

        // Show success animation
        await _showSuccessAnimation();

        // Animate transition to home page
        await _animateToHomePage();
      } else {
        _showErrorWithRetry('فشل في رفع الصورة بعد عدة محاولات');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Upload error: $e');
      }

      String errorMessage = 'حدث خطأ أثناء رفع الصورة';
      if (e.toString().contains('network')) {
        errorMessage = 'خطأ في الاتصال بالإنترنت. تحقق من اتصالك وحاول مرة أخرى';
      } else if (e.toString().contains('storage')) {
        errorMessage = 'خطأ في تخزين الصورة. حاول مرة أخرى';
      } else if (e.toString().contains('auth')) {
        errorMessage = 'خطأ في المصادقة. يرجى تسجيل الدخول مرة أخرى';
      }

      _showErrorWithRetry(errorMessage);
    } finally {
      if (mounted) {
        setState(() => _isUploading = false);
      }
    }
  }

  Future<void> _showSuccessAnimation() async {
    // Start celebration animation
    _celebrationController.forward();

    // Multiple haptic feedback for celebration
    HapticFeedback.heavyImpact();
    await Future.delayed(const Duration(milliseconds: 100));
    HapticFeedback.mediumImpact();
    await Future.delayed(const Duration(milliseconds: 100));
    HapticFeedback.lightImpact();

    // Show success message with animation
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              AnimatedBuilder(
                animation: _celebrationAnimation,
                builder: (context, child) {
                  return Transform.scale(
                    scale: 1.0 + (_celebrationAnimation.value * 0.3),
                    child: const Icon(Icons.celebration, color: Colors.white),
                  );
                },
              ),
              const SizedBox(width: 8),
              const Expanded(
                child: Text(
                  'تم رفع الصورة بنجاح! 🎉',
                  style: TextStyle(fontWeight: FontWeight.w600),
                ),
              ),
            ],
          ),
          backgroundColor: AppColors.success,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          duration: const Duration(seconds: 2),
        ),
      );
    }

    await Future.delayed(const Duration(milliseconds: 1500));
  }

  void _showErrorWithRetry(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(Icons.error_outline, color: Colors.white),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      message,
                      style: const TextStyle(fontWeight: FontWeight.w600),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () {
                      ScaffoldMessenger.of(context).hideCurrentSnackBar();
                      _uploadAndContinue();
                    },
                    child: const Text(
                      'إعادة المحاولة',
                      style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                    ),
                  ),
                ],
              ),
            ],
          ),
          backgroundColor: AppColors.error,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          duration: const Duration(seconds: 5),
        ),
      );
    }
  }

  Future<void> _animateToHomePage() async {
    // Fade out current page
    await _fadeController.reverse();

    if (mounted) {
      Navigator.of(context).pushReplacement(
        PageRouteBuilder(
          pageBuilder: (context, animation, secondaryAnimation) => const HomePage(),
          transitionDuration: const Duration(milliseconds: 800),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            const begin = Offset(1.0, 0.0);
            const end = Offset.zero;
            const curve = Curves.easeInOutCubic;

            var tween = Tween(begin: begin, end: end).chain(
              CurveTween(curve: curve),
            );

            return SlideTransition(
              position: animation.drive(tween),
              child: FadeTransition(
                opacity: animation,
                child: child,
              ),
            );
          },
        ),
      );
    }
  }



  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.success,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    final currentUser = authProvider.currentUser;

    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Color(0xFF1565C0), // Deep blue
              Color(0xFF42A5F5), // Light blue
              Color(0xFF90CAF9), // Very light blue
            ],
            stops: [0.0, 0.6, 1.0],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Stack(
          children: [
            // Animated background elements
            _buildAnimatedBackground(),
            // Main content
            _buildMainContent(),
          ],
        ),
      ),
    );
  }

  Widget _buildAnimatedBackground() {
    return Positioned.fill(
      child: AnimatedBuilder(
        animation: _borderAnimation,
        builder: (context, child) {
          return Stack(
            children: [
              // Floating circles
              Positioned(
                top: 100 + (_borderAnimation.value * 20),
                right: 50 + (_borderAnimation.value * 10),
                child: Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white.withValues(alpha: 0.1),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.white.withValues(alpha: 0.2),
                        blurRadius: 20,
                        spreadRadius: 5,
                      ),
                    ],
                  ),
                ),
              ),
              Positioned(
                bottom: 150 + (_borderAnimation.value * -15),
                left: 30 + (_borderAnimation.value * 8),
                child: Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white.withValues(alpha: 0.08),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.white.withValues(alpha: 0.15),
                        blurRadius: 15,
                        spreadRadius: 3,
                      ),
                    ],
                  ),
                ),
              ),
              Positioned(
                top: 200 + (_borderAnimation.value * -10),
                left: 80 + (_borderAnimation.value * 5),
                child: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white.withValues(alpha: 0.06),
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildMainContent() {
    final authProvider = Provider.of<AuthProvider>(context);
    final currentUser = authProvider.currentUser;

    return SafeArea(
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: ScaleTransition(
          scale: _scaleAnimation,
          child: Center(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(24),
              child: Container(
                constraints: const BoxConstraints(maxWidth: 400),
                padding: const EdgeInsets.all(32),
                decoration: BoxDecoration(
                  // Glassmorphism effect
                  color: Colors.white.withValues(alpha: 0.15),
                  borderRadius: BorderRadius.circular(32),
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.2),
                    width: 1.5,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 40,
                      offset: const Offset(0, 20),
                      spreadRadius: 0,
                    ),
                    BoxShadow(
                      color: Colors.white.withValues(alpha: 0.1),
                      blurRadius: 20,
                      offset: const Offset(0, -10),
                      spreadRadius: 0,
                    ),
                  ],
                ),
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Welcome text with emoji
                      const Text(
                        '🎉 مرحباً بك!',
                        style: TextStyle(
                          fontSize: 32,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                          shadows: [
                            Shadow(
                              color: Colors.black26,
                              offset: Offset(0, 2),
                              blurRadius: 4,
                            ),
                          ],
                        ),
                        textAlign: TextAlign.center,
                      ).animate().fadeIn(duration: 600.ms).slideY(begin: -0.3),

                      const SizedBox(height: 16),

                      // Description
                      const Text(
                        'أضف صورة شخصية واضحة لإكمال ملفك الشخصي',
                        style: TextStyle(
                          fontSize: 18,
                          color: Colors.white,
                          height: 1.6,
                          fontWeight: FontWeight.w500,
                          shadows: [
                            Shadow(
                              color: Colors.black26,
                              offset: Offset(0, 1),
                              blurRadius: 2,
                            ),
                          ],
                        ),
                        textAlign: TextAlign.center,
                      ).animate(delay: 200.ms).fadeIn(duration: 600.ms).slideY(begin: 0.3),

                      const SizedBox(height: 40),

                      // Image section with animations
                      _buildEnhancedImageSection(),
                      const SizedBox(height: 24),

                      // User name with bold styling
                      if (currentUser?.fullName != null) ...[
                        Text(
                          currentUser!.fullName,
                          style: const TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            shadows: [
                              Shadow(
                                color: Colors.black26,
                                offset: Offset(0, 2),
                                blurRadius: 4,
                              ),
                            ],
                          ),
                          textAlign: TextAlign.center,
                        ).animate(delay: 400.ms).fadeIn(duration: 600.ms).scale(begin: const Offset(0.8, 0.8)),
                        const SizedBox(height: 12),
                      ],

                      // Phone number
                      if (currentUser?.phone != null) ...[
                        Text(
                          currentUser!.phone,
                          style: const TextStyle(
                            fontSize: 16,
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                            shadows: [
                              Shadow(
                                color: Colors.black26,
                                offset: Offset(0, 1),
                                blurRadius: 2,
                              ),
                            ],
                          ),
                          textAlign: TextAlign.center,
                        ).animate(delay: 500.ms).fadeIn(duration: 600.ms),
                        const SizedBox(height: 32),
                      ],

                      // Slide to continue button
                      _buildSlideToConfirmButton(),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEnhancedImageSection() {
    return Center(
      child: GestureDetector(
        onTap: _showImageSourceDialog,
        child: AnimatedBuilder(
          animation: Listenable.merge([_pulseAnimation, _borderAnimation]),
          builder: (context, child) {
            return Transform.scale(
              scale: _pulseAnimation.value,
              child: Container(
                width: 180,
                height: 180,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                    colors: [
                      AppColors.primary.withValues(alpha: 0.2 + _borderAnimation.value * 0.3),
                      AppColors.secondary.withValues(alpha: 0.1 + _borderAnimation.value * 0.2),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.primary.withValues(alpha: 0.3),
                      blurRadius: 25,
                      offset: const Offset(0, 10),
                      spreadRadius: 2,
                    ),
                    BoxShadow(
                      color: AppColors.secondary.withValues(alpha: 0.2),
                      blurRadius: 40,
                      offset: const Offset(0, 20),
                    ),
                  ],
                ),
                child: Container(
                  margin: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 15,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: _isImageLoading
                      ? Container(
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            gradient: LinearGradient(
                              colors: [
                                AppColors.primary.withValues(alpha: 0.1),
                                AppColors.secondary.withValues(alpha: 0.1),
                              ],
                            ),
                          ),
                          child: const Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                CircularProgressIndicator(
                                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                                  strokeWidth: 3,
                                ),
                                SizedBox(height: 12),
                                Text(
                                  'جاري تحميل الصورة...',
                                  style: TextStyle(
                                    color: AppColors.primary,
                                    fontSize: 12,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        )
                      : _selectedImage != null
                          ? ClipOval(
                              child: Stack(
                                children: [
                                  // Image with shimmer effect
                                  Container(
                                    width: 164,
                                    height: 164,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      gradient: LinearGradient(
                                        colors: [
                                          Colors.grey.shade200,
                                          Colors.grey.shade100,
                                          Colors.grey.shade200,
                                        ],
                                        stops: const [0.0, 0.5, 1.0],
                                        begin: Alignment.topLeft,
                                        end: Alignment.bottomRight,
                                      ),
                                    ),
                                  ),
                                  // Actual image
                                  kIsWeb
                                      ? FutureBuilder<Uint8List>(
                                          future: _selectedImage!.readAsBytes(),
                                          builder: (context, snapshot) {
                                            if (snapshot.hasData) {
                                              return Image.memory(
                                                snapshot.data!,
                                                width: 164,
                                                height: 164,
                                                fit: BoxFit.cover,
                                              ).animate().fadeIn(duration: 300.ms);
                                            }
                                            return const Center(
                                              child: CircularProgressIndicator(
                                                valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                                                strokeWidth: 2,
                                              ),
                                            );
                                          },
                                        )
                                      : Image.file(
                                          File(_selectedImage!.path),
                                          width: 164,
                                          height: 164,
                                          fit: BoxFit.cover,
                                        ).animate().fadeIn(duration: 300.ms),

                                  // Success checkmark overlay
                                  if (!_isUploading)
                                    Positioned(
                                      top: 8,
                                      left: 8,
                                      child: Container(
                                        width: 28,
                                        height: 28,
                                        decoration: BoxDecoration(
                                          color: AppColors.success,
                                          shape: BoxShape.circle,
                                          boxShadow: [
                                            BoxShadow(
                                              color: Colors.black.withValues(alpha: 0.2),
                                              blurRadius: 6,
                                              offset: const Offset(0, 2),
                                            ),
                                          ],
                                        ),
                                        child: const Icon(
                                          Icons.check,
                                          color: Colors.white,
                                          size: 16,
                                        ),
                                      ).animate().scale(delay: 200.ms, duration: 400.ms),
                                    ),

                                  // Camera icon overlay
                                  Positioned(
                                    bottom: 8,
                                    right: 8,
                                    child: Container(
                                      width: 36,
                                      height: 36,
                                      decoration: BoxDecoration(
                                        color: AppColors.primary,
                                        shape: BoxShape.circle,
                                        boxShadow: [
                                          BoxShadow(
                                            color: Colors.black.withValues(alpha: 0.2),
                                            blurRadius: 8,
                                            offset: const Offset(0, 2),
                                          ),
                                        ],
                                      ),
                                      child: const Icon(
                                        Icons.edit,
                                        color: Colors.white,
                                        size: 18,
                                      ),
                                    ).animate().scale(delay: 100.ms, duration: 300.ms),
                                  ),
                                ],
                              ),
                            )
                      : Container(
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            gradient: LinearGradient(
                              colors: [
                                AppColors.primary.withValues(alpha: 0.05),
                                AppColors.secondary.withValues(alpha: 0.05),
                              ],
                            ),
                          ),
                          child: Stack(
                            children: [
                              const Center(
                                child: Icon(
                                  Icons.add_a_photo_rounded,
                                  size: 56,
                                  color: AppColors.primary,
                                ),
                              ),
                              // Camera icon in bottom right
                              Positioned(
                                bottom: 8,
                                right: 8,
                                child: Container(
                                  width: 36,
                                  height: 36,
                                  decoration: BoxDecoration(
                                    color: AppColors.primary,
                                    shape: BoxShape.circle,
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withValues(alpha: 0.2),
                                        blurRadius: 8,
                                        offset: const Offset(0, 2),
                                      ),
                                    ],
                                  ),
                                  child: const Icon(
                                    Icons.camera_alt,
                                    color: Colors.white,
                                    size: 18,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                ),
              ),
            );
          },
        ),
      ),
    ).animate(delay: 300.ms).scale(begin: const Offset(0.5, 0.5)).fadeIn(duration: 800.ms);
  }

  Widget _buildSlideToConfirmButton() {
    return Column(
      children: [
        // Validation message
        if (_selectedImage == null) ...[
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: AppColors.warning.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.warning.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: const Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: AppColors.warning,
                  size: 20,
                ),
                SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'يرجى اختيار صورة شخصية للمتابعة',
                    style: TextStyle(
                      color: AppColors.warning,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ).animate().fadeIn(duration: 400.ms).slideY(begin: 0.2),
          const SizedBox(height: 20),
        ],

        // Slide to confirm button with enhanced animations
        AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
          child: SlideToConfirmButton(
            text: 'اسحب للمتابعة',
            slideText: _isUploading
                ? 'جاري الرفع...'
                : _selectedImage != null
                    ? 'اسحب للمتابعة'
                    : 'اختر صورة أولاً',
            onConfirm: _selectedImage != null && !_isUploading && !_isImageLoading
                ? _uploadAndContinue
                : null,
            isEnabled: _selectedImage != null && !_isUploading && !_isImageLoading,
            isLoading: _isUploading,
            icon: _isUploading
                ? Icons.cloud_upload
                : _selectedImage != null
                    ? Icons.arrow_forward
                    : Icons.add_a_photo,
            backgroundColor: _selectedImage != null && !_isUploading
                ? AppColors.primary
                : AppColors.textSecondary,
            foregroundColor: Colors.white,
            width: double.infinity,
            height: 64,
          ),
        ).animate(delay: 600.ms).slideX(begin: -0.3).fadeIn(duration: 800.ms),

        // Upload progress indicator
        if (_isUploading) ...[
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: const Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    strokeWidth: 2,
                  ),
                ),
                SizedBox(width: 12),
                Text(
                  'جاري رفع الصورة وحفظ البيانات...',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ).animate().fadeIn(duration: 300.ms).slideY(begin: 0.2),
        ],
      ],
    );
  }
}
