import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:speech_to_text/speech_to_text.dart';
import 'package:flutter_tts/flutter_tts.dart';
import '../../constants/app_theme.dart';
import '../../services/supabase_service.dart';
import '../../services/darija_nlp_service.dart';
import '../../services/city_history_service.dart';
import '../../services/audio_notification_service.dart';
import '../../models/trip_model.dart';
import '../../widgets/trip_card.dart';
import '../trip/trip_details_page.dart';
import '../../utils/navigation_utils.dart';

class EnhancedVoiceAssistantPage extends StatefulWidget {
  const EnhancedVoiceAssistantPage({super.key});

  @override
  State<EnhancedVoiceAssistantPage> createState() =>
      _EnhancedVoiceAssistantPageState();
}

class _EnhancedVoiceAssistantPageState extends State<EnhancedVoiceAssistantPage>
    with TickerProviderStateMixin {
  // Services
  final SpeechToText _speechToText = SpeechToText();
  final FlutterTts _flutterTts = FlutterTts();
  final DarijaNLPService _nlpService = DarijaNLPService();
  final CityHistoryService _historyService = CityHistoryService();
  final AudioNotificationService _audioService = AudioNotificationService();

  // State variables
  bool _speechEnabled = false;
  bool _isListening = false;
  bool _isSearching = false;
  bool _isSpeaking = false;
  String _recognizedText = '';
  String _statusMessage =
      'مرحبا بيك ف تطبيق سفرني، شنو الرحلة اللي باغي تمشي ليها؟';
  String _lastSearchQuery = '';
  List<TripModel> _foundTrips = [];
  List<String> _quickFilters = [];

  // Animation controllers
  late AnimationController _rippleController;
  late AnimationController _pulseController;
  late AnimationController _glowController;
  late Animation<double> _rippleAnimation;
  late Animation<double> _pulseAnimation;
  late Animation<double> _glowAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeServices();
  }

  void _initializeAnimations() {
    // Ripple animation for listening state - faster for AI-like effect
    _rippleController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    _rippleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _rippleController, curve: Curves.easeOut),
    );

    // Pulse animation for the main circle - subtle breathing effect
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.08).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    // Glow animation for active states - smooth intensity changes
    _glowController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _glowAnimation = Tween<double>(begin: 0.4, end: 1.0).animate(
      CurvedAnimation(parent: _glowController, curve: Curves.easeInOut),
    );
  }

  Future<void> _initializeServices() async {
    try {
      // Initialize services
      await _historyService.init();
      await _audioService.initialize();
      await _initializeSpeech();
      await _initializeTts();
      await _loadQuickFilters();

      // Start with idle animation for AI-like presence
      _startIdleAnimation();
    } catch (e) {
      setState(() {
        _statusMessage = 'حدث خطأ في تهيئة المساعد الصوتي';
      });
    }
  }

  Future<void> _initializeSpeech() async {
    try {
      _speechEnabled = await _speechToText.initialize(
        onError: _onSpeechError,
        onStatus: _onSpeechStatus,
        debugLogging: false, // Reduce console spam
      );

      if (!_speechEnabled) {
        setState(() {
          _statusMessage = kIsWeb
              ? 'يرجى السماح للمتصفح بالوصول للميكروفون'
              : 'التعرف على الصوت غير متاح';
        });
      }
    } catch (e) {
      setState(() {
        _statusMessage = kIsWeb
            ? 'فشل في الوصول للميكروفون. تحقق من إعدادات المتصفح'
            : 'فشل في تهيئة التعرف على الصوت';
      });
    }
  }

  Future<void> _initializeTts() async {
    try {
      // Configure TTS for Moroccan Arabic with friendly tone
      try {
        await _flutterTts.setLanguage('ar-MA'); // Moroccan Arabic
      } catch (e) {
        await _flutterTts.setLanguage('ar'); // Fallback to standard Arabic
      }

      await _flutterTts.setSpeechRate(0.6); // Slower for better understanding
      await _flutterTts.setVolume(0.9);
      await _flutterTts.setPitch(0.8); // Lower pitch for friendliness

      // Set up TTS callbacks
      _flutterTts.setStartHandler(() {
        if (mounted) setState(() => _isSpeaking = true);
      });

      _flutterTts.setCompletionHandler(() {
        if (mounted) setState(() => _isSpeaking = false);
      });

      _flutterTts.setErrorHandler((msg) {
        if (mounted) setState(() => _isSpeaking = false);
      });
    } catch (e) {
      // Silent error handling for production
    }
  }

  Future<void> _loadQuickFilters() async {
    try {
      final filters = await _historyService.getQuickFilters();
      setState(() {
        _quickFilters = filters;
      });
    } catch (e) {
      if (kDebugMode) {
        print('Failed to load quick filters: $e');
      }
    }
  }

  void _onSpeechError(error) {
    setState(() {
      _statusMessage = 'حدث خطأ في التعرف على الصوت. حاول مرة أخرى';
      _isListening = false;
    });
    _stopAnimations();
    _audioService.playSearchError();
  }

  void _onSpeechStatus(status) {
    if (status == 'done' || status == 'notListening') {
      setState(() => _isListening = false);
      _stopAnimations();
      _audioService.playListeningStop();
    }
  }

  void _startListening() async {
    if (!_speechEnabled || _isListening) return;

    setState(() {
      _isListening = true;
      _statusMessage = 'أستمع إليك...';
      _recognizedText = '';
    });

    _startAnimations();
    await _audioService.playListeningStart();

    try {
      await _speechToText.listen(
        onResult: _onSpeechResult,
        listenFor: const Duration(seconds: 10),
        pauseFor: const Duration(seconds: 3),
        listenOptions: SpeechListenOptions(
          partialResults: true,
          onDevice: false,
          listenMode: ListenMode.confirmation,
        ),
        localeId: 'ar-MA', // Moroccan Arabic
        onSoundLevelChange: (level) {
          // Update glow intensity based on sound level
          if (_isListening && mounted) {
            _glowController.animateTo(level.clamp(0.3, 1.0));
          }
        },
      );
    } catch (e) {
      _onSpeechError(e.toString());
    }
  }

  void _onSpeechResult(result) {
    if (mounted) {
      setState(() {
        _recognizedText = result.recognizedWords;
      });

      if (result.finalResult) {
        _processVoiceInput(result.recognizedWords);
      }
    }
  }

  void _stopListening() async {
    if (_isListening) {
      await _speechToText.stop();
      setState(() => _isListening = false);
      _stopAnimations();
      await _audioService.playListeningStop();
    }
  }

  void _startAnimations() {
    // Start listening animations - AI-like active state
    _rippleController.repeat();
    _pulseController.repeat(reverse: true);
    _glowController.repeat(reverse: true);
  }

  void _stopAnimations() {
    // Stop listening animations and return to idle state
    _rippleController.stop();
    _rippleController.reset();

    // Return to idle state with subtle breathing
    _startIdleAnimation();
  }

  void _startIdleAnimation() {
    // Subtle breathing effect when not listening - AI-like presence
    _pulseController.stop();
    _glowController.stop();

    // Very subtle pulse for idle state
    _pulseController.repeat(reverse: true);
    _glowController.animateTo(0.4, duration: const Duration(milliseconds: 500));
  }

  Future<void> _processVoiceInput(String text) async {
    if (text.trim().isEmpty) return;

    if (mounted) {
      setState(() {
        _statusMessage = 'أفهم طلبك...';
        _isSearching = true;
        _foundTrips = [];
        _lastSearchQuery = text; // Store the last search
      });
    }

    try {
      // Parse the query using enhanced NLP
      final query = _nlpService.parseQuery(text);

      if (query.isValid) {
        // Generate Moroccan Arabic success response
        final response =
            'زوين! فهمت عليك، بغيتي تمشي من ${query.fromCity} ل ${query.toCity}';
        if (mounted) setState(() => _statusMessage = response);
        await _speak(response);

        // Search for trips
        await _searchTrips(query);

        // Add to history
        await _historyService.addCitySearch(query.fromCity!, query.toCity!);
        await _loadQuickFilters();
      } else {
        // Generate Moroccan Arabic error response
        const errorResponse =
            'معليش، ما فهمتش المدن اللي بغيتي. عاود قول ليا فين بغيتي تمشي؟';
        if (mounted) {
          setState(() {
            _statusMessage = errorResponse;
            _isSearching = false;
          });
        }
        await _speak(errorResponse);
        await _audioService.playSearchError();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _statusMessage = 'حدث خطأ أثناء البحث';
          _isSearching = false;
        });
      }
      await _audioService.playSearchError();
    }
  }

  Future<void> _searchTrips(TripQuery query) async {
    try {
      final allTrips = await SupabaseService.getTrips();

      // Enhanced filtering with better matching
      final filteredTrips = allTrips.where((trip) {
        final fromMatch = trip.fromCity
                .toLowerCase()
                .contains(query.fromCity!.toLowerCase()) ||
            query.fromCity!.toLowerCase().contains(trip.fromCity.toLowerCase());
        final toMatch =
            trip.toCity.toLowerCase().contains(query.toCity!.toLowerCase()) ||
                query.toCity!.toLowerCase().contains(trip.toCity.toLowerCase());
        return fromMatch && toMatch;
      }).toList();

      if (mounted) {
        setState(() {
          _foundTrips = filteredTrips;
          _isSearching = false;
        });
      }

      // Announce results with Darija phrases
      String resultMessage;
      if (filteredTrips.isEmpty) {
        resultMessage = 'ما لقيتش حتى رحلة، جرب من مدينة أخرى.';
      } else {
        resultMessage = 'آه شوف، هادي الرحلات اللي لقيت لك.';
      }

      if (mounted) setState(() => _statusMessage = resultMessage);
      await _speakDarija(resultMessage);

      if (filteredTrips.isNotEmpty) {
        await _audioService.playSearchSuccess();
      } else {
        await _audioService.playSearchError();
      }
    } catch (e) {
      setState(() {
        _statusMessage = 'حدث خطأ أثناء البحث عن الرحلات';
        _isSearching = false;
      });
      await _audioService.playSearchError();
    }
  }

  Future<void> _speak(String text) async {
    if (text.trim().isEmpty) return;

    try {
      await _flutterTts.stop();
      await _flutterTts.speak(text);
    } catch (e) {
      // Silent error handling for production
    }
  }

  Future<void> _speakDarija(String text) async {
    if (text.trim().isEmpty) return;

    try {
      await _flutterTts.stop();

      // Configure for Moroccan Arabic with natural settings
      await _flutterTts.setLanguage("ar");
      await _flutterTts.setSpeechRate(0.5); // Slower for clarity
      await _flutterTts.setPitch(0.8); // Lower pitch for friendliness
      await _flutterTts.setVolume(0.9);

      await _flutterTts.speak(text);
    } catch (e) {
      // Silent error handling for production
    }
  }

  void _navigateToTripDetails(TripModel trip) {
    if (trip.id.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('خطأ: معرف الرحلة غير صحيح'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    try {
      NavigationUtils.pushWithTransition(
        context,
        TripDetailsPage(tripId: trip.id),
        type: TransitionType.slide,
        duration: const Duration(milliseconds: 400),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('خطأ في التنقل إلى تفاصيل الرحلة'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _onQuickFilterTap(String filter) async {
    final parts = filter.split(' ⇌ ');
    if (parts.length == 2) {
      final query = TripQuery(
        fromCity: parts[0].trim(),
        toCity: parts[1].trim(),
        originalText: 'من ${parts[0].trim()} ل ${parts[1].trim()}',
        confidence: 1.0,
      );

      setState(() {
        _statusMessage = 'أبحث عن الرحلات...';
        _isSearching = true;
        _foundTrips = [];
      });

      await _searchTrips(query);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: IgnorePointer(
        ignoring: _isListening, // Disable interaction while recording
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppColors.background,
                AppColors.primary.withOpacity(0.05),
                AppColors.secondary.withOpacity(0.03),
              ],
            ),
          ),
          child: SafeArea(
            child: LayoutBuilder(
              builder: (context, constraints) {
                final availableHeight = constraints.maxHeight;

                return Column(
                  children: [
                    // Top Navigation - Fixed height
                    _buildTopNavigation(),

                    // Scrollable content
                    Expanded(
                      child: SingleChildScrollView(
                        physics: const BouncingScrollPhysics(),
                        child: Column(
                          children: [
                            // Voice Interface Section - Fixed height
                            SizedBox(
                              height: availableHeight * 0.4,
                              child: _buildVoiceInterface(),
                            ),

                            // Listening Indicator
                            if (_isListening) _buildListeningIndicator(),

                            // Last Search Section (Combined with Quick Filters)
                            if (_quickFilters.isNotEmpty ||
                                _lastSearchQuery.isNotEmpty)
                              _buildLastSearchSection(),

                            // Search Results Section
                            _buildSearchResults(),

                            // Bottom Padding
                            const SizedBox(height: 40),
                          ],
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTopNavigation() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Title
          const Text(
            'المساعد الصوتي',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          // Home Button
          Container(
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.9),
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: IconButton(
              onPressed: () => Navigator.of(context).pop(),
              icon: const Icon(Icons.home, color: AppColors.primary),
              tooltip: 'العودة للرئيسية',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildListeningIndicator() {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 10),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: AppColors.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: AppColors.primary.withOpacity(0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
            ),
          ),
          const SizedBox(width: 8),
          const Text(
            'يتم الاستماع الآن...',
            style: TextStyle(
              color: AppColors.primary,
              fontWeight: FontWeight.w600,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVoiceInterface() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Main Voice Circle with Animations
          GestureDetector(
            onTap: _isListening ? _stopListening : _startListening,
            child: AnimatedBuilder(
              animation: Listenable.merge([
                _rippleAnimation,
                _pulseAnimation,
                _glowAnimation,
              ]),
              builder: (context, child) {
                return Stack(
                  alignment: Alignment.center,
                  children: [
                    // Enhanced ripple effects (only when listening)
                    if (_isListening) ...[
                      _buildEnhancedRipple(
                          160 + (_rippleAnimation.value * 100), 0.06),
                      _buildEnhancedRipple(
                          140 + (_rippleAnimation.value * 80), 0.08),
                      _buildEnhancedRipple(
                          120 + (_rippleAnimation.value * 60), 0.12),
                      _buildEnhancedRipple(
                          100 + (_rippleAnimation.value * 40), 0.16),
                    ],

                    // Main AI-like circle with dynamic states
                    Transform.scale(
                      scale: _isListening ? _pulseAnimation.value : 1.0,
                      child: Container(
                        width: 140,
                        height: 140,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: RadialGradient(
                            colors: _isListening
                                ? [
                                    AppColors.primary.withOpacity(
                                        _glowAnimation.value * 0.95),
                                    AppColors.primary.withOpacity(
                                        _glowAnimation.value * 0.8),
                                    AppColors.primary.withOpacity(
                                        _glowAnimation.value * 0.5),
                                    AppColors.primary.withOpacity(
                                        _glowAnimation.value * 0.2),
                                  ]
                                : [
                                    AppColors.primary.withOpacity(0.7),
                                    AppColors.primary.withOpacity(0.5),
                                    AppColors.primary.withOpacity(0.3),
                                    AppColors.primary.withOpacity(0.1),
                                  ],
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: _isListening
                                  ? AppColors.primary
                                      .withOpacity(_glowAnimation.value * 0.7)
                                  : AppColors.primary.withOpacity(0.3),
                              blurRadius: _isListening ? 35 : 20,
                              spreadRadius: _isListening ? 10 : 5,
                            ),
                            BoxShadow(
                              color: Colors.white.withOpacity(0.15),
                              blurRadius: 12,
                              spreadRadius: -3,
                              offset: const Offset(-3, -3),
                            ),
                          ],
                        ),
                        child: Container(
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: Colors.white
                                .withOpacity(_isListening ? 0.15 : 0.1),
                            border: Border.all(
                              color: Colors.white
                                  .withOpacity(_isListening ? 0.3 : 0.2),
                              width: _isListening ? 3 : 2,
                            ),
                          ),
                          child: Icon(
                            _isListening ? Icons.mic : Icons.mic_none,
                            size: _isListening ? 56 : 52,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
          ),

          const SizedBox(height: 24),

          // Status Message with Glassmorphism
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 24),
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
            constraints: const BoxConstraints(
              minHeight: 60,
              maxWidth: 400,
            ),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.95),
              borderRadius: BorderRadius.circular(24),
              border: Border.all(
                color: Colors.white.withOpacity(0.3),
                width: 1.5,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.08),
                  blurRadius: 20,
                  offset: const Offset(0, 8),
                ),
                BoxShadow(
                  color: Colors.white.withOpacity(0.6),
                  blurRadius: 10,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: Text(
              _statusMessage,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
                height: 1.4,
              ),
              textAlign: TextAlign.center,
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
          ),

          const SizedBox(height: 20),

          // Recognized Text with Enhanced Design
          if (_recognizedText.isNotEmpty)
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 24),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppColors.primary.withOpacity(0.15),
                    AppColors.primary.withOpacity(0.08),
                  ],
                ),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: AppColors.primary.withOpacity(0.25),
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.primary.withOpacity(0.1),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.format_quote,
                    color: AppColors.primary.withOpacity(0.7),
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _recognizedText,
                      style: TextStyle(
                        fontSize: 15,
                        fontStyle: FontStyle.italic,
                        color: AppColors.primary.withOpacity(0.9),
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Icon(
                    Icons.format_quote,
                    color: AppColors.primary.withOpacity(0.7),
                    size: 20,
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildRipple(double size, double opacity) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
          color: AppColors.primary.withOpacity(opacity),
          width: 2,
        ),
      ),
    );
  }

  Widget _buildEnhancedRipple(double size, double opacity) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
          color: AppColors.primary.withOpacity(opacity),
          width: 3,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withOpacity(opacity * 0.5),
            blurRadius: 8,
            spreadRadius: 2,
          ),
        ],
      ),
    );
  }

  Widget _buildLastSearchSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.95),
        borderRadius: BorderRadius.circular(24),
        border: Border.all(
          color: AppColors.primary.withOpacity(0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 16,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Title
          Row(
            children: [
              const Icon(
                Icons.search,
                color: AppColors.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              const Text(
                '🔎 آخر بحث صوتي',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
          if (_lastSearchQuery.isNotEmpty) ...[
            const SizedBox(height: 4),
            const Text(
              'اضغط للبحث مرة أخرى',
              style: TextStyle(
                fontSize: 12,
                color: AppColors.textSecondary,
              ),
            ),
          ],
          const SizedBox(height: 12),

          // Quick Filter Chips
          if (_quickFilters.isNotEmpty)
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: _quickFilters.map((filter) {
                return GestureDetector(
                  onTap: () => _onQuickFilterTap(filter),
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          AppColors.primary.withOpacity(0.1),
                          AppColors.primary.withOpacity(0.05),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: AppColors.primary.withOpacity(0.3),
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(
                          Icons.history,
                          size: 16,
                          color: AppColors.primary,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          filter,
                          style: const TextStyle(
                            fontSize: 13,
                            color: AppColors.primary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),
        ],
      ),
    );
  }

  Widget _buildSearchResults() {
    if (_isSearching) {
      return Container(
        margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
        padding: const EdgeInsets.all(40),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.9),
          borderRadius: BorderRadius.circular(24),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.08),
              blurRadius: 16,
              offset: const Offset(0, 6),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
              strokeWidth: 3,
            ),
            const SizedBox(height: 20),
            const Text(
              'جاري البحث عن الرحلات...',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      );
    }

    if (_foundTrips.isEmpty) {
      return Container(
        margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
        padding: const EdgeInsets.all(32),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.9),
          borderRadius: BorderRadius.circular(24),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.08),
              blurRadius: 16,
              offset: const Offset(0, 6),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(50),
              ),
              child: Icon(
                Icons.mic_none,
                size: 48,
                color: AppColors.primary.withOpacity(0.7),
              ),
            ),
            const SizedBox(height: 20),
            const Text(
              'اضغط على الميكروفون وقل وجهتك',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Text(
              'مثال: "بغيت نمشي من الدار البيضاء للناظور"',
              style: TextStyle(
                fontSize: 14,
                color: AppColors.textSecondary.withOpacity(0.8),
                fontStyle: FontStyle.italic,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Results Header
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppColors.primary.withOpacity(0.1),
                  AppColors.primary.withOpacity(0.05),
                ],
              ),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: AppColors.primary.withOpacity(0.2),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.directions_bus,
                    color: AppColors.primary,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  'الرحلات المتاحة (${_foundTrips.length})',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),

          // Trip Results
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _foundTrips.length,
            itemBuilder: (context, index) {
              final trip = _foundTrips[index];
              return Container(
                margin: const EdgeInsets.only(bottom: 12),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.06),
                      blurRadius: 12,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: TripCard(
                  trip: trip,
                  onTap: () => _navigateToTripDetails(trip),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _rippleController.dispose();
    _pulseController.dispose();
    _glowController.dispose();
    _speechToText.stop();
    _flutterTts.stop();
    _audioService.dispose();
    super.dispose();
  }
}
