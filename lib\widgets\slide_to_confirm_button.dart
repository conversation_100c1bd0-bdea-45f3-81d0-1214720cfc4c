import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../constants/app_theme.dart';

/// A slide-to-continue button with swipe interaction
class SlideToConfirmButton extends StatefulWidget {
  final String text;
  final VoidCallback? onConfirm;
  final bool isEnabled;
  final bool isLoading;
  final IconData? icon;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final double? width;
  final double? height;
  final String? slideText;

  const SlideToConfirmButton({
    super.key,
    required this.text,
    this.onConfirm,
    this.isEnabled = true,
    this.isLoading = false,
    this.icon,
    this.backgroundColor,
    this.foregroundColor,
    this.width,
    this.height,
    this.slideText,
  });

  @override
  State<SlideToConfirmButton> createState() => _SlideToConfirmButtonState();
}

class _SlideToConfirmButtonState extends State<SlideToConfirmButton>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _glowController;
  late AnimationController _pulseController;
  late Animation<double> _slideAnimation;
  late Animation<double> _glowAnimation;
  late Animation<double> _pulseAnimation;
  
  double _dragPosition = 0.0;
  bool _isDragging = false;
  bool _isCompleted = false;
  
  @override
  void initState() {
    super.initState();
    
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _glowController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    
    _slideAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _slideController, curve: Curves.easeInOut),
    );
    
    _glowAnimation = Tween<double>(begin: 0.3, end: 1.0).animate(
      CurvedAnimation(parent: _glowController, curve: Curves.easeInOut),
    );
    
    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.05).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );
    
    // Start glow animation
    _glowController.repeat(reverse: true);
    _pulseController.repeat(reverse: true);
  }
  
  @override
  void dispose() {
    _slideController.dispose();
    _glowController.dispose();
    _pulseController.dispose();
    super.dispose();
  }
  
  void _onPanStart(DragStartDetails details) {
    if (!widget.isEnabled || widget.isLoading || _isCompleted) return;
    
    setState(() {
      _isDragging = true;
    });
    
    HapticFeedback.lightImpact();
  }
  
  void _onPanUpdate(DragUpdateDetails details) {
    if (!widget.isEnabled || widget.isLoading || _isCompleted) return;
    
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final buttonWidth = renderBox.size.width;
    final thumbWidth = 60.0; // Width of the sliding thumb
    final maxSlide = buttonWidth - thumbWidth - 8; // Account for padding
    
    setState(() {
      _dragPosition = (details.localPosition.dx - thumbWidth / 2)
          .clamp(0.0, maxSlide);
    });
    
    // Provide haptic feedback at certain thresholds
    final progress = _dragPosition / maxSlide;
    if (progress > 0.8 && !_isCompleted) {
      HapticFeedback.mediumImpact();
    }
  }
  
  void _onPanEnd(DragEndDetails details) {
    if (!widget.isEnabled || widget.isLoading || _isCompleted) return;
    
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final buttonWidth = renderBox.size.width;
    final thumbWidth = 60.0;
    final maxSlide = buttonWidth - thumbWidth - 8;
    final progress = _dragPosition / maxSlide;
    
    if (progress >= 0.8) {
      // Slide completed
      setState(() {
        _isCompleted = true;
        _dragPosition = maxSlide;
      });
      
      HapticFeedback.heavyImpact();
      _slideController.forward();
      
      // Call the callback after animation
      Future.delayed(const Duration(milliseconds: 300), () {
        widget.onConfirm?.call();
      });
    } else {
      // Slide back to start
      setState(() {
        _dragPosition = 0.0;
        _isDragging = false;
      });
      
      HapticFeedback.lightImpact();
    }
  }
  
  @override
  Widget build(BuildContext context) {
    final buttonWidth = widget.width ?? double.infinity;
    final buttonHeight = widget.height ?? 60.0;
    final thumbWidth = 60.0;
    
    return AnimatedBuilder(
      animation: Listenable.merge([_glowAnimation, _pulseAnimation]),
      builder: (context, child) {
        return Transform.scale(
          scale: _pulseAnimation.value,
          child: Container(
            width: buttonWidth,
            height: buttonHeight,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(buttonHeight / 2),
              gradient: LinearGradient(
                colors: [
                  (widget.backgroundColor ?? AppColors.primary)
                      .withOpacity(_glowAnimation.value),
                  (widget.backgroundColor ?? AppColors.primary)
                      .withOpacity(_glowAnimation.value * 0.8),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              boxShadow: [
                BoxShadow(
                  color: (widget.backgroundColor ?? AppColors.primary)
                      .withOpacity(_glowAnimation.value * 0.4),
                  blurRadius: 20,
                  offset: const Offset(0, 8),
                ),
                BoxShadow(
                  color: (widget.backgroundColor ?? AppColors.primary)
                      .withOpacity(_glowAnimation.value * 0.2),
                  blurRadius: 40,
                  offset: const Offset(0, 16),
                ),
              ],
            ),
            child: Stack(
              children: [
                // Background text
                Center(
                  child: Text(
                    widget.slideText ?? 'اسحب للمتابعة',
                    style: TextStyle(
                      color: (widget.foregroundColor ?? Colors.white)
                          .withOpacity(0.7),
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                
                // Sliding thumb
                AnimatedPositioned(
                  duration: _isDragging 
                      ? Duration.zero 
                      : const Duration(milliseconds: 300),
                  curve: Curves.easeOut,
                  left: 4 + _dragPosition,
                  top: 4,
                  child: GestureDetector(
                    onPanStart: _onPanStart,
                    onPanUpdate: _onPanUpdate,
                    onPanEnd: _onPanEnd,
                    child: Container(
                      width: thumbWidth - 8,
                      height: buttonHeight - 8,
                      decoration: BoxDecoration(
                        color: widget.foregroundColor ?? Colors.white,
                        borderRadius: BorderRadius.circular((buttonHeight - 8) / 2),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.2),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Center(
                        child: _isCompleted
                            ? Icon(
                                Icons.check,
                                color: widget.backgroundColor ?? AppColors.primary,
                                size: 24,
                              )
                            : Icon(
                                widget.icon ?? Icons.arrow_forward,
                                color: widget.backgroundColor ?? AppColors.primary,
                                size: 24,
                              ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
