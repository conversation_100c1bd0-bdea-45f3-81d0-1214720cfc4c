import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:timeago/timeago.dart' as timeago;
import '../constants/app_theme.dart';
import '../models/trip_model.dart';
import '../providers/trip_provider.dart';
import '../utils/navigation_utils.dart';
import '../pages/trip_leader/edit_trip_page.dart';

class EnhancedDriverTripCard extends StatefulWidget {
  final TripModel trip;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onShare;

  const EnhancedDriverTripCard({
    super.key,
    required this.trip,
    this.onEdit,
    this.onDelete,
    this.onShare,
  });

  @override
  State<EnhancedDriverTripCard> createState() => _EnhancedDriverTripCardState();
}

class _EnhancedDriverTripCardState extends State<EnhancedDriverTripCard>
    with TickerProviderStateMixin {
  late AnimationController _rippleController;
  late AnimationController _hoverController;
  late Animation<double> _rippleAnimation;
  late Animation<double> _scaleAnimation;
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();

    // Set Arabic locale for timeago
    timeago.setLocaleMessages('ar', timeago.ArMessages());

    // Initialize animations
    _rippleController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _hoverController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _rippleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _rippleController,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.02,
    ).animate(CurvedAnimation(
      parent: _hoverController,
      curve: Curves.easeInOut,
    ));

    // Start ripple animation for active trips
    if (widget.trip.status == 'active' || widget.trip.status == 'published') {
      _rippleController.repeat();
    }
  }

  @override
  void dispose() {
    _rippleController.dispose();
    _hoverController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => _onHover(true),
      onExit: (_) => _onHover(false),
      child: GestureDetector(
        onTapDown: (_) => _hoverController.forward(),
        onTapUp: (_) => _hoverController.reverse(),
        onTapCancel: () => _hoverController.reverse(),
        child: AnimatedBuilder(
          animation: _scaleAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimation.value,
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black
                          .withValues(alpha: _isHovered ? 0.15 : 0.08),
                      blurRadius: _isHovered ? 16 : 12,
                      offset: Offset(0, _isHovered ? 8 : 4),
                      spreadRadius: _isHovered ? 2 : 0,
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildHeader(),
                      _buildContent(),
                      _buildActionButtons(),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  void _onHover(bool isHovered) {
    setState(() {
      _isHovered = isHovered;
    });
    if (isHovered) {
      _hoverController.forward();
    } else {
      _hoverController.reverse();
    }
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primary.withValues(alpha: 0.05),
            AppColors.secondary.withValues(alpha: 0.05),
          ],
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Route with enhanced styling
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: AppColors.primary.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.location_on,
                        color: AppColors.primary,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '${widget.trip.fromCity} ← ${widget.trip.toCity}',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: AppColors.textPrimary,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 4),
                          Row(
                            children: [
                              Icon(
                                Icons.access_time,
                                size: 14,
                                color: AppColors.textSecondary,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                'تم النشر ${timeago.format(widget.trip.createdAt, locale: 'ar')}',
                                style: const TextStyle(
                                  fontSize: 12,
                                  color: AppColors.textSecondary,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          // Status indicator with enhanced animation
          _buildStatusIndicator(),
        ],
      ),
    );
  }

  Widget _buildStatusIndicator() {
    final isActive =
        widget.trip.status == 'active' || widget.trip.status == 'published';
    final isCancelled = widget.trip.status == 'cancelled';

    if (isActive) {
      return AnimatedBuilder(
        animation: _rippleAnimation,
        builder: (context, child) {
          return Stack(
            alignment: Alignment.center,
            children: [
              // Outer ripple
              Container(
                width: 32 + (_rippleAnimation.value * 16),
                height: 32 + (_rippleAnimation.value * 16),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: AppColors.success.withValues(
                    alpha: 0.2 * (1 - _rippleAnimation.value),
                  ),
                ),
              ),
              // Middle ripple
              Container(
                width: 24 + (_rippleAnimation.value * 8),
                height: 24 + (_rippleAnimation.value * 8),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: AppColors.success.withValues(
                    alpha: 0.4 * (1 - _rippleAnimation.value),
                  ),
                ),
              ),
              // Core circle
              Container(
                width: 16,
                height: 16,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: AppColors.success,
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.success.withValues(alpha: 0.3),
                      blurRadius: 8,
                      spreadRadius: 2,
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      );
    } else if (isCancelled) {
      return Container(
        width: 16,
        height: 16,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: AppColors.error,
          boxShadow: [
            BoxShadow(
              color: AppColors.error.withValues(alpha: 0.3),
              blurRadius: 4,
              spreadRadius: 1,
            ),
          ],
        ),
      );
    }

    return const SizedBox.shrink();
  }

  Widget _buildContent() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          // Trip details in a modern grid layout
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.surfaceVariant.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: _buildInfoItem(
                        Icons.calendar_today,
                        '${widget.trip.departureDate.day}/${widget.trip.departureDate.month}',
                        'التاريخ',
                        AppColors.primary,
                      ),
                    ),
                    Expanded(
                      child: _buildInfoItem(
                        Icons.access_time,
                        widget.trip.departureTime,
                        'الوقت',
                        AppColors.secondary,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: _buildInfoItem(
                        Icons.airline_seat_recline_normal,
                        '${widget.trip.availableSeats}/${widget.trip.totalSeats}',
                        'المقاعد',
                        AppColors.accent,
                      ),
                    ),
                    Expanded(
                      child: _buildInfoItem(
                        Icons.attach_money,
                        '${widget.trip.price.toStringAsFixed(0)} درهم',
                        'السعر',
                        AppColors.success,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Car info section
          if (widget.trip.carModel != null || widget.trip.carPlate != null) ...[
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: AppColors.primary.withValues(alpha: 0.1),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.directions_car,
                    color: AppColors.primary,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (widget.trip.carModel != null)
                          Text(
                            widget.trip.carModel!,
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: AppColors.textPrimary,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        if (widget.trip.carPlate != null)
                          Text(
                            widget.trip.carPlate!,
                            style: const TextStyle(
                              fontSize: 12,
                              color: AppColors.textSecondary,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildInfoItem(
      IconData icon, String value, String label, Color color) {
    return Container(
      padding: const EdgeInsets.all(8),
      child: Column(
        children: [
          Icon(icon, size: 20, color: color),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
            overflow: TextOverflow.ellipsis,
          ),
          Text(
            label,
            style: const TextStyle(
              fontSize: 10,
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surfaceVariant.withValues(alpha: 0.5),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildActionButton(
            icon: Icons.edit,
            label: 'تعديل',
            color: AppColors.primary,
            onTap: () => _handleEdit(),
          ),
          _buildActionButton(
            icon: Icons.delete,
            label: 'حذف',
            color: AppColors.error,
            onTap: () => _handleDelete(),
          ),
          _buildActionButton(
            icon: Icons.share,
            label: 'مشاركة',
            color: AppColors.secondary,
            onTap: () => _handleShare(),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: color.withValues(alpha: 0.2),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: color, size: 22),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: color,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _handleEdit() {
    if (widget.onEdit != null) {
      widget.onEdit!();
    } else {
      // Navigate to EditTripPage with pre-filled data
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => EditTripPage(tripId: widget.trip.id),
        ),
      );
    }
  }

  void _handleDelete() {
    if (widget.onDelete != null) {
      widget.onDelete!();
    } else {
      _showDeleteConfirmation();
    }
  }

  void _handleShare() {
    if (widget.onShare != null) {
      widget.onShare!();
    } else {
      _shareTrip();
    }
  }

  void _showDeleteConfirmation() async {
    final confirmed = await NavigationUtils.showConfirmationDialog(
      context,
      title: 'حذف الرحلة',
      content:
          'هل أنت متأكد من حذف هذه الرحلة؟ لا يمكن التراجع عن هذا الإجراء.',
      confirmText: 'حذف',
      cancelText: 'إلغاء',
      confirmColor: AppColors.error,
    );

    if (confirmed == true && mounted) {
      final tripProvider = Provider.of<TripProvider>(context, listen: false);
      final success = await tripProvider.deleteTrip(widget.trip.id);

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حذف الرحلة بنجاح'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    }
  }

  void _shareTrip() async {
    final tripDetails = '''
🚗 رحلة من ${widget.trip.fromCity} إلى ${widget.trip.toCity}
📅 التاريخ: ${widget.trip.departureDate.day}/${widget.trip.departureDate.month}/${widget.trip.departureDate.year}
⏰ الوقت: ${widget.trip.departureTime}
💰 السعر: ${widget.trip.price.toStringAsFixed(0)} درهم
🪑 المقاعد المتاحة: ${widget.trip.availableSeats}/${widget.trip.totalSeats}
${widget.trip.carModel != null ? '🚙 السيارة: ${widget.trip.carModel}' : ''}
${widget.trip.carPlate != null ? '🔢 اللوحة: ${widget.trip.carPlate}' : ''}

احجز الآن عبر تطبيق سفرني!
https://safarni.app/r/${widget.trip.id}
    ''';

    try {
      // Use clipboard for sharing (compatible with all platforms)
      await Clipboard.setData(ClipboardData(text: tripDetails));

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم نسخ تفاصيل الرحلة إلى الحافظة'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('فشل في نسخ تفاصيل الرحلة'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }
}
