import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_theme.dart';
import '../../providers/auth_provider.dart';
import '../../models/user_model.dart';
import '../profile/profile_image_upload_page.dart';

class ProfileUploadDemo extends StatelessWidget {
  const ProfileUploadDemo({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Profile Upload Demo'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text(
              'Profile Upload Page Demo',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppColors.primary,
              ),
            ),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: () => _navigateToProfileUpload(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text(
                'Open Profile Upload Page',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'This will simulate the post-signup flow',
              style: TextStyle(
                color: AppColors.textSecondary,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToProfileUpload(BuildContext context) {
    // Create a mock user for testing
    final mockUser = UserModel(
      id: 'demo-user-id',
      phone: '+212612345678',
      fullName: 'أحمد محمد',
      role: 'traveler',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    // Set the mock user in the auth provider
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    authProvider.setCurrentUser(mockUser);

    // Navigate to the profile upload page
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const ProfileImageUploadPage(),
      ),
    );
  }
}
