import 'package:flutter_test/flutter_test.dart';
import 'package:safarni/providers/auth_provider.dart';

void main() {
  group('AuthProvider Phone Validation Tests', () {
    late AuthProvider authProvider;

    setUp(() {
      authProvider = AuthProvider();
    });

    test('Phone validation should require phone for authentication', () {
      // Phone validation is now used for authentication

      // Empty phone should be invalid
      expect(authProvider.validatePhoneRequired(''), isNotNull);
      expect(authProvider.validatePhoneRequired(null), isNotNull);

      // Valid Moroccan phone should be valid
      expect(authProvider.validatePhoneRequired('+212612345678'), isNull);
      expect(authProvider.validatePhoneRequired('**********'), isNull);

      // Invalid phone format should be invalid
      expect(authProvider.validatePhoneRequired('invalid-phone'), isNotNull);
    });

    test('Phone validation should handle various Moroccan phone formats', () {
      // Valid phones
      expect(authProvider.validatePhoneRequired('+212612345678'), isNull);
      expect(authProvider.validatePhoneRequired('**********'), isNull);
      expect(authProvider.validatePhoneRequired('+212712345678'), isNull);
      expect(authProvider.validatePhoneRequired('**********'), isNull);

      // Invalid phones
      expect(authProvider.validatePhoneRequired('+212512345678'),
          isNotNull); // Wrong prefix
      expect(authProvider.validatePhoneRequired('**********'),
          isNotNull); // Wrong prefix
      expect(authProvider.validatePhoneRequired('+21261234567'),
          isNotNull); // Too short
      expect(authProvider.validatePhoneRequired('061234567'),
          isNotNull); // Too short
    });

    test('Other validation methods should work correctly', () {
      // Test password validation
      expect(authProvider.validatePassword('123456'), isNull);
      expect(authProvider.validatePassword('12345'), isNotNull);
      expect(authProvider.validatePassword(''), isNotNull);
      expect(authProvider.validatePassword(null), isNotNull);

      // Test full name validation
      expect(authProvider.validateFullName('John Doe'), isNull);
      expect(authProvider.validateFullName('J'), isNotNull);
      expect(authProvider.validateFullName(''), isNotNull);
      expect(authProvider.validateFullName(null), isNotNull);

      // Test phone validation (optional)
      expect(authProvider.validatePhone(''), isNull);
      expect(authProvider.validatePhone(null), isNull);
      expect(authProvider.validatePhone('+212612345678'), isNull);
      expect(authProvider.validatePhone('**********'), isNull);
      expect(authProvider.validatePhone('invalid-phone'), isNotNull);
    });
  });
}
