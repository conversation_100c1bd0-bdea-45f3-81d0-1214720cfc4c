import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../constants/app_theme.dart';
import '../../models/trip_model.dart';
import '../../providers/trip_provider.dart';
import '../../providers/auth_provider.dart';
import '../../utils/navigation_utils.dart';

class EditTripPage extends StatefulWidget {
  final String tripId;

  const EditTripPage({super.key, required this.tripId});

  @override
  State<EditTripPage> createState() => _EditTripPageState();
}

class _EditTripPageState extends State<EditTripPage> {
  final _formKey = GlobalKey<FormState>();

  // Controllers
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _fromCityController = TextEditingController();
  final _toCityController = TextEditingController();
  final _priceController = TextEditingController();
  final _totalSeatsController = TextEditingController();
  final _carModelController = TextEditingController();
  final _carPlateController = TextEditingController();
  final _notesController = TextEditingController();

  // State variables
  TripModel? _originalTrip;
  DateTime? _departureDate;
  TimeOfDay? _departureTime;
  DateTime? _returnDate;
  TimeOfDay? _returnTime;
  String _tripType = 'mixed';
  bool _allowInstantBooking = false;
  bool _isPriceNegotiable = false;
  bool _isLoading = true;
  bool _isSaving = false;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadTripData();
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _fromCityController.dispose();
    _toCityController.dispose();
    _priceController.dispose();
    _totalSeatsController.dispose();
    _carModelController.dispose();
    _carPlateController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _loadTripData() async {
    try {
      final tripProvider = Provider.of<TripProvider>(context, listen: false);
      await tripProvider.loadTripById(widget.tripId);

      final trip = tripProvider.selectedTrip;
      if (trip != null) {
        setState(() {
          _originalTrip = trip;
          _populateFields(trip);
          _isLoading = false;
        });
      } else {
        setState(() {
          _error = 'لم يتم العثور على الرحلة';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _error = 'فشل في تحميل بيانات الرحلة';
        _isLoading = false;
      });
    }
  }

  void _populateFields(TripModel trip) {
    _titleController.text = trip.title;
    _descriptionController.text = trip.description;
    _fromCityController.text = trip.fromCity;
    _toCityController.text = trip.toCity;
    _priceController.text = trip.price.toString();
    _totalSeatsController.text = trip.totalSeats.toString();
    _carModelController.text = trip.carModel ?? '';
    _carPlateController.text = trip.carPlate ?? trip.carPlateNumber ?? '';
    _notesController.text = trip.notes ?? '';

    _departureDate = trip.departureDate;
    _returnDate = trip.returnDate;
    _tripType = trip.tripType;
    _allowInstantBooking = trip.allowInstantBooking;
    _isPriceNegotiable = trip.isPriceNegotiable;

    // Parse time strings
    if (trip.departureTime.isNotEmpty) {
      final parts = trip.departureTime.split(':');
      if (parts.length >= 2) {
        _departureTime = TimeOfDay(
          hour: int.tryParse(parts[0]) ?? 0,
          minute: int.tryParse(parts[1]) ?? 0,
        );
      }
    }

    if (trip.returnTime != null && trip.returnTime!.isNotEmpty) {
      final parts = trip.returnTime!.split(':');
      if (parts.length >= 2) {
        _returnTime = TimeOfDay(
          hour: int.tryParse(parts[0]) ?? 0,
          minute: int.tryParse(parts[1]) ?? 0,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('تعديل الرحلة'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          if (!_isLoading && _originalTrip != null)
            TextButton(
              onPressed: _isSaving ? null : _saveTrip,
              child: _isSaving
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Text(
                      'حفظ',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
            ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('جاري تحميل بيانات الرحلة...'),
          ],
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: AppColors.error,
            ),
            const SizedBox(height: 16),
            Text(
              _error!,
              style: const TextStyle(
                fontSize: 16,
                color: AppColors.error,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadTripData,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    return Form(
      key: _formKey,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionHeader('معلومات الرحلة الأساسية'),
            _buildBasicInfoSection(),
            const SizedBox(height: 24),
            _buildSectionHeader('التوقيت والتاريخ'),
            _buildDateTimeSection(),
            const SizedBox(height: 24),
            _buildSectionHeader('السعر والمقاعد'),
            _buildPriceSeatsSection(),
            const SizedBox(height: 24),
            _buildSectionHeader('معلومات السيارة'),
            _buildCarInfoSection(),
            const SizedBox(height: 24),
            _buildSectionHeader('إعدادات إضافية'),
            _buildAdditionalSettings(),
            const SizedBox(height: 32),
            _buildSaveButton(),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: AppColors.textPrimary,
        ),
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            TextFormField(
              controller: _titleController,
              decoration: const InputDecoration(
                labelText: 'عنوان الرحلة',
                prefixIcon: Icon(Icons.title),
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال عنوان الرحلة';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'وصف الرحلة',
                prefixIcon: Icon(Icons.description),
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال وصف الرحلة';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _fromCityController,
                    decoration: const InputDecoration(
                      labelText: 'من',
                      prefixIcon: Icon(Icons.location_on),
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'يرجى إدخال مدينة الانطلاق';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: TextFormField(
                    controller: _toCityController,
                    decoration: const InputDecoration(
                      labelText: 'إلى',
                      prefixIcon: Icon(Icons.location_on_outlined),
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'يرجى إدخال مدينة الوصول';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDateTimeSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Departure Date and Time
            Row(
              children: [
                Expanded(
                  child: InkWell(
                    onTap: _selectDepartureDate,
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          const Icon(Icons.calendar_today),
                          const SizedBox(width: 8),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text('تاريخ الانطلاق'),
                              Text(
                                _departureDate != null
                                    ? DateFormat('dd/MM/yyyy')
                                        .format(_departureDate!)
                                    : 'اختر التاريخ',
                                style: TextStyle(
                                  color: _departureDate != null
                                      ? AppColors.textPrimary
                                      : AppColors.textSecondary,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: InkWell(
                    onTap: _selectDepartureTime,
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          const Icon(Icons.access_time),
                          const SizedBox(width: 8),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text('وقت الانطلاق'),
                              Text(
                                _departureTime != null
                                    ? _departureTime!.format(context)
                                    : 'اختر الوقت',
                                style: TextStyle(
                                  color: _departureTime != null
                                      ? AppColors.textPrimary
                                      : AppColors.textSecondary,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPriceSeatsSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _priceController,
                decoration: const InputDecoration(
                  labelText: 'السعر (درهم)',
                  prefixIcon: Icon(Icons.attach_money),
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'يرجى إدخال السعر';
                  }
                  if (double.tryParse(value) == null) {
                    return 'يرجى إدخال رقم صحيح';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: TextFormField(
                controller: _totalSeatsController,
                decoration: const InputDecoration(
                  labelText: 'عدد المقاعد',
                  prefixIcon: Icon(Icons.airline_seat_recline_normal),
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'يرجى إدخال عدد المقاعد';
                  }
                  if (int.tryParse(value) == null) {
                    return 'يرجى إدخال رقم صحيح';
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCarInfoSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            TextFormField(
              controller: _carModelController,
              decoration: const InputDecoration(
                labelText: 'نوع السيارة',
                prefixIcon: Icon(Icons.directions_car),
                border: OutlineInputBorder(),
                hintText: 'مثال: تويوتا كامري',
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _carPlateController,
              decoration: const InputDecoration(
                labelText: 'رقم اللوحة',
                prefixIcon: Icon(Icons.confirmation_number),
                border: OutlineInputBorder(),
                hintText: 'مثال: أ-123456-ب',
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _notesController,
              decoration: const InputDecoration(
                labelText: 'ملاحظات إضافية',
                prefixIcon: Icon(Icons.note),
                border: OutlineInputBorder(),
                hintText: 'أي معلومات إضافية للمسافرين',
              ),
              maxLines: 2,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdditionalSettings() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            DropdownButtonFormField<String>(
              value: _tripType,
              decoration: const InputDecoration(
                labelText: 'نوع الرحلة',
                prefixIcon: Icon(Icons.group),
                border: OutlineInputBorder(),
              ),
              items: const [
                DropdownMenuItem(value: 'mixed', child: Text('مختلط')),
                DropdownMenuItem(value: 'women_only', child: Text('نساء فقط')),
                DropdownMenuItem(
                    value: 'family_only', child: Text('عائلات فقط')),
              ],
              onChanged: (value) {
                setState(() {
                  _tripType = value ?? 'mixed';
                });
              },
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('السماح بالحجز الفوري'),
              subtitle: const Text('يمكن للمسافرين الحجز مباشرة دون موافقة'),
              value: _allowInstantBooking,
              onChanged: (value) {
                setState(() {
                  _allowInstantBooking = value;
                });
              },
            ),
            SwitchListTile(
              title: const Text('السعر قابل للتفاوض'),
              subtitle: const Text('يمكن للمسافرين التفاوض على السعر'),
              value: _isPriceNegotiable,
              onChanged: (value) {
                setState(() {
                  _isPriceNegotiable = value;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _isSaving ? null : _saveTrip,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 2,
        ),
        child: _isSaving
            ? const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ),
                  SizedBox(width: 12),
                  Text('جاري الحفظ...'),
                ],
              )
            : const Text(
                'حفظ التغييرات',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
      ),
    );
  }

  Future<void> _selectDepartureDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate:
          _departureDate ?? DateTime.now().add(const Duration(days: 1)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (date != null) {
      setState(() {
        _departureDate = date;
      });
    }
  }

  Future<void> _selectDepartureTime() async {
    final time = await showTimePicker(
      context: context,
      initialTime: _departureTime ?? TimeOfDay.now(),
    );

    if (time != null) {
      setState(() {
        _departureTime = time;
      });
    }
  }

  Future<void> _saveTrip() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_departureDate == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار تاريخ الانطلاق'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    if (_departureTime == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار وقت الانطلاق'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      final tripProvider = Provider.of<TripProvider>(context, listen: false);

      final updatedTrip = _originalTrip!.copyWith(
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim(),
        fromCity: _fromCityController.text.trim(),
        toCity: _toCityController.text.trim(),
        departureDate: _departureDate!,
        departureTime:
            '${_departureTime!.hour.toString().padLeft(2, '0')}:${_departureTime!.minute.toString().padLeft(2, '0')}',
        price: double.parse(_priceController.text),
        totalSeats: int.parse(_totalSeatsController.text),
        carModel: _carModelController.text.trim().isEmpty
            ? null
            : _carModelController.text.trim(),
        carPlate: _carPlateController.text.trim().isEmpty
            ? null
            : _carPlateController.text.trim(),
        notes: _notesController.text.trim().isEmpty
            ? null
            : _notesController.text.trim(),
        tripType: _tripType,
        allowInstantBooking: _allowInstantBooking,
        isPriceNegotiable: _isPriceNegotiable,
        updatedAt: DateTime.now(),
      );

      final success = await tripProvider.updateTrip(updatedTrip);

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تحديث الرحلة بنجاح'),
            backgroundColor: AppColors.success,
          ),
        );
        Navigator.of(context).pop(true); // Return true to indicate success
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في تحديث الرحلة: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }
}
