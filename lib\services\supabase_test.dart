import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Simple test service to verify Supabase connection works on web
class SupabaseTest {
  static final SupabaseClient _client = Supabase.instance.client;

  /// Test basic connection to Supabase
  static Future<bool> testConnection() async {
    try {
      if (kDebugMode) {
        print('🔍 Testing Supabase connection...');
      }

      // Simple test - try to access auth
      final user = _client.auth.currentUser;
      
      if (kDebugMode) {
        print('✅ Connection successful. Current user: ${user?.id ?? 'none'}');
      }
      
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Connection test failed: $e');
        print('Error type: ${e.runtimeType}');
      }
      return false;
    }
  }

  /// Test signup functionality specifically
  static Future<bool> testSignUp(String email, String password) async {
    try {
      if (kDebugMode) {
        print('🔍 Testing signup with email: $email');
      }

      final response = await _client.auth.signUp(
        email: email,
        password: password,
      );

      if (kDebugMode) {
        print('✅ Signup test successful');
        print('User ID: ${response.user?.id}');
        print('Session: ${response.session != null ? 'exists' : 'null'}');
      }

      return response.user != null;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Signup test failed: $e');
        print('Error type: ${e.runtimeType}');
        
        // Print more details for debugging
        if (e.toString().contains('fetch')) {
          print('🌐 This appears to be a network/CORS issue');
          print('💡 Check:');
          print('   - Supabase CORS settings');
          print('   - Network connectivity');
          print('   - Browser console for more details');
        }
      }
      return false;
    }
  }

  /// Get detailed error information
  static String getErrorDetails(dynamic error) {
    final details = StringBuffer();
    details.writeln('Error: $error');
    details.writeln('Type: ${error.runtimeType}');
    
    if (error.toString().contains('fetch')) {
      details.writeln('Likely cause: Network/CORS issue');
      details.writeln('Solutions:');
      details.writeln('1. Check Supabase CORS settings');
      details.writeln('2. Verify network connectivity');
      details.writeln('3. Check browser console');
    }
    
    return details.toString();
  }
}
