-- Storage Metadata Update Function for Driver License Upload
-- Execute this in Supabase SQL Editor to create the RPC function

-- =====================================================
-- CREATE METADATA UPDATE FUNCTION
-- =====================================================

-- Drop function if it exists
DROP FUNCTION IF EXISTS update_storage_metadata(text, text, jsonb);

-- Create function to update storage object metadata
CREATE OR REPLACE FUNCTION update_storage_metadata(
  bucket_name text,
  object_name text,
  new_metadata jsonb
)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Update the metadata for the specified storage object
  UPDATE storage.objects 
  SET metadata = new_metadata,
      updated_at = now()
  WHERE bucket_id = bucket_name 
    AND name = object_name
    AND owner = auth.uid(); -- Security: only allow updating own files
    
  -- Check if any rows were updated
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Object not found or access denied: %/%', bucket_name, object_name;
  END IF;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION update_storage_metadata(text, text, jsonb) TO authenticated;

-- =====================================================
-- ALTERNATIVE: SIMPLER METADATA UPDATE FUNCTION
-- =====================================================

-- Drop function if it exists
DROP FUNCTION IF EXISTS set_license_owner(text, text);

-- Create simpler function specifically for license uploads
CREATE OR REPLACE FUNCTION set_license_owner(
  object_path text,
  owner_id text
)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Update metadata for driver license with owner field
  UPDATE storage.objects 
  SET metadata = jsonb_build_object('owner', owner_id),
      updated_at = now()
  WHERE bucket_id = 'driver-licenses' 
    AND name = object_path
    AND (metadata->>'owner' IS NULL OR metadata->>'owner' = auth.uid()::text);
    
  -- Check if any rows were updated
  IF NOT FOUND THEN
    RAISE EXCEPTION 'License object not found or access denied: %', object_path;
  END IF;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION set_license_owner(text, text) TO authenticated;

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Test the function (replace with actual values)
-- SELECT update_storage_metadata('driver-licenses', 'licenses/license_test.jpg', '{"owner": "user-id-here"}');

-- Check if functions were created
SELECT 
  proname as function_name,
  prosrc as function_body
FROM pg_proc 
WHERE proname IN ('update_storage_metadata', 'set_license_owner');

-- =====================================================
-- USAGE EXAMPLES
-- =====================================================

/*
-- Example 1: Using the general metadata function
SELECT update_storage_metadata(
  'driver-licenses',
  'licenses/license_550e8400-e29b-41d4-a716-************.jpg',
  '{"owner": "550e8400-e29b-41d4-a716-************"}'::jsonb
);

-- Example 2: Using the specific license function
SELECT set_license_owner(
  'licenses/license_550e8400-e29b-41d4-a716-************.jpg',
  '550e8400-e29b-41d4-a716-************'
);

-- Example 3: Check metadata after update
SELECT 
  name,
  metadata,
  metadata->>'owner' as owner,
  created_at,
  updated_at
FROM storage.objects 
WHERE bucket_id = 'driver-licenses' 
  AND name LIKE 'licenses/license_%'
ORDER BY created_at DESC;
*/

-- =====================================================
-- SECURITY NOTES
-- =====================================================

/*
SECURITY FEATURES:
1. SECURITY DEFINER - Function runs with elevated privileges
2. owner = auth.uid() - Only allows updating own files
3. Specific bucket check - Only works with driver-licenses bucket
4. Error handling - Proper exceptions for debugging

USAGE IN FLUTTER:
await _supabase.rpc('set_license_owner', params: {
  'object_path': 'licenses/license_<userId>.jpg',
  'owner_id': currentUser.id
});

FALLBACK APPROACH:
If RPC fails, the upload function will still return the file path,
allowing the app to continue working even if metadata isn't set.
*/
