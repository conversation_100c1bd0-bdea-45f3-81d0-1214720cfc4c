import 'package:flutter/material.dart';
import '../../constants/app_theme.dart';
import '../../models/trip_model.dart';
import '../../widgets/enhanced_trip_leader_card.dart';

class EnhancedCardDemo extends StatelessWidget {
  const EnhancedCardDemo({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Enhanced Trip Leader Card Demo'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Enhanced Trip Leader Cards',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Modern, animated trip cards with status indicators and action buttons',
              style: TextStyle(
                fontSize: 16,
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: 24),
            
            // Active Trip Card
            const Text(
              'Active Trip (with animated ripple)',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 12),
            EnhancedTripLeaderCard(
              trip: _createSampleTrip(status: 'active'),
            ),
            
            const SizedBox(height: 32),
            
            // Published Trip Card
            const Text(
              'Published Trip',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 12),
            EnhancedTripLeaderCard(
              trip: _createSampleTrip(status: 'published'),
            ),
            
            const SizedBox(height: 32),
            
            // Cancelled Trip Card
            const Text(
              'Cancelled Trip',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 12),
            EnhancedTripLeaderCard(
              trip: _createSampleTrip(status: 'cancelled'),
            ),
            
            const SizedBox(height: 32),
            
            // Completed Trip Card
            const Text(
              'Completed Trip',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 12),
            EnhancedTripLeaderCard(
              trip: _createSampleTrip(status: 'completed'),
            ),
            
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  TripModel _createSampleTrip({required String status}) {
    return TripModel(
      id: 'demo_trip_${status}_${DateTime.now().millisecondsSinceEpoch}',
      leaderId: 'demo_leader',
      title: 'رحلة تجريبية',
      description: 'رحلة تجريبية لعرض التصميم الجديد',
      fromCity: 'الرباط',
      toCity: 'الدار البيضاء',
      departureDate: DateTime.now().add(const Duration(days: 1)),
      departureTime: '08:00',
      price: 50.0,
      totalSeats: 4,
      availableSeats: 2,
      status: status,
      carModel: 'تويوتا كامري',
      carPlate: 'أ-123456-ب',
      createdAt: DateTime.now().subtract(const Duration(minutes: 30)),
      updatedAt: DateTime.now(),
    );
  }
}
