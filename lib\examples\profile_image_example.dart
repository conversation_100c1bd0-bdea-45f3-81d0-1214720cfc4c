import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../constants/app_theme.dart';
import '../providers/auth_provider.dart';
import '../widgets/profile_image_picker.dart';

/// Example page demonstrating how to use the ProfileImagePicker widget
/// This shows the professional profile image upload feature in action
class ProfileImageExample extends StatefulWidget {
  const ProfileImageExample({super.key});

  @override
  State<ProfileImageExample> createState() => _ProfileImageExampleState();
}

class _ProfileImageExampleState extends State<ProfileImageExample> {
  String? _currentImageUrl;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Profile Image Upload Example'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          final currentUser = authProvider.currentUser;
          
          if (currentUser == null) {
            return const Center(
              child: Text(
                'Please log in to use this feature',
                style: TextStyle(fontSize: 16),
              ),
            );
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const SizedBox(height: 40),
                
                // Title
                const Text(
                  'Professional Profile Image Upload',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                
                // Description
                const Text(
                  'Tap the image below to upload or change your profile picture. '
                  'The image will be automatically uploaded to Supabase Storage '
                  'and saved to your profile.',
                  style: TextStyle(
                    fontSize: 16,
                    color: AppColors.textSecondary,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 40),
                
                // Profile Image Picker - Small Size
                const Text(
                  'Small Size (radius: 40)',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
                const SizedBox(height: 16),
                ProfileImagePicker(
                  userId: currentUser.id,
                  initialImageUrl: currentUser.profileImageUrl,
                  radius: 40,
                  showEditIcon: true,
                  onImageChanged: () {
                    // Refresh the UI or perform any action after image change
                    setState(() {});
                  },
                  onImageUrlChanged: (newUrl) {
                    setState(() {
                      _currentImageUrl = newUrl;
                    });
                  },
                ),
                const SizedBox(height: 40),
                
                // Profile Image Picker - Medium Size
                const Text(
                  'Medium Size (radius: 60)',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
                const SizedBox(height: 16),
                ProfileImagePicker(
                  userId: currentUser.id,
                  initialImageUrl: _currentImageUrl ?? currentUser.profileImageUrl,
                  radius: 60,
                  showEditIcon: true,
                  onImageChanged: () {
                    setState(() {});
                  },
                  onImageUrlChanged: (newUrl) {
                    setState(() {
                      _currentImageUrl = newUrl;
                    });
                  },
                ),
                const SizedBox(height: 40),
                
                // Profile Image Picker - Large Size
                const Text(
                  'Large Size (radius: 80)',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
                const SizedBox(height: 16),
                ProfileImagePicker(
                  userId: currentUser.id,
                  initialImageUrl: _currentImageUrl ?? currentUser.profileImageUrl,
                  radius: 80,
                  showEditIcon: true,
                  onImageChanged: () {
                    setState(() {});
                  },
                  onImageUrlChanged: (newUrl) {
                    setState(() {
                      _currentImageUrl = newUrl;
                    });
                  },
                ),
                const SizedBox(height: 40),
                
                // Profile Image Picker - Without Edit Icon
                const Text(
                  'Without Edit Icon (read-only)',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
                const SizedBox(height: 16),
                ProfileImagePicker(
                  userId: currentUser.id,
                  initialImageUrl: _currentImageUrl ?? currentUser.profileImageUrl,
                  radius: 50,
                  showEditIcon: false, // No edit functionality
                ),
                const SizedBox(height: 40),
                
                // Current Image URL Display
                if (_currentImageUrl != null) ...[
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: AppColors.surfaceVariant,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: AppColors.border),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Current Image URL:',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: AppColors.textPrimary,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          _currentImageUrl!,
                          style: const TextStyle(
                            fontSize: 12,
                            color: AppColors.textSecondary,
                            fontFamily: 'monospace',
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 20),
                ],
                
                // Usage Instructions
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.05),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: AppColors.primary.withValues(alpha: 0.2),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.info_outline,
                            color: AppColors.primary,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          const Text(
                            'How to Use',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: AppColors.primary,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      const Text(
                        '1. Import the ProfileImagePicker widget\n'
                        '2. Provide the user ID and optional initial image URL\n'
                        '3. Set the desired radius and edit icon visibility\n'
                        '4. Handle image change callbacks as needed\n'
                        '5. The widget handles all upload logic automatically',
                        style: TextStyle(
                          fontSize: 14,
                          color: AppColors.textSecondary,
                          height: 1.5,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 40),
              ],
            ),
          );
        },
      ),
    );
  }
}
