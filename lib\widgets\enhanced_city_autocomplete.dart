import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../constants/app_theme.dart';
import '../services/darija_nlp_service.dart';
import '../services/supabase_service.dart';

class EnhancedCityAutocomplete extends StatefulWidget {
  final String? initialValue;
  final String hintText;
  final Function(String) onCitySelected;
  final bool enabled;

  const EnhancedCityAutocomplete({
    super.key,
    this.initialValue,
    required this.hintText,
    required this.onCitySelected,
    this.enabled = true,
  });

  @override
  State<EnhancedCityAutocomplete> createState() => _EnhancedCityAutocompleteState();
}

class _EnhancedCityAutocompleteState extends State<EnhancedCityAutocomplete> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  final DarijaNLPService _nlpService = DarijaNLPService();
  
  List<String> _suggestions = [];
  List<String> _allCities = [];
  bool _isLoading = false;
  bool _showSuggestions = false;

  @override
  void initState() {
    super.initState();
    _controller.text = widget.initialValue ?? '';
    _loadCities();
    
    _controller.addListener(_onTextChanged);
    _focusNode.addListener(_onFocusChanged);
  }

  Future<void> _loadCities() async {
    setState(() => _isLoading = true);
    
    try {
      // Get cities from NLP service (Moroccan cities with variations)
      final moroccanCities = _nlpService.getSupportedCities();
      
      // Get cities from Supabase (actual trip data)
      final dbCities = await SupabaseService.getDistinctCities();
      
      // Combine and deduplicate
      final allCities = <String>{};
      allCities.addAll(moroccanCities);
      allCities.addAll(dbCities);
      
      setState(() {
        _allCities = allCities.toList()..sort();
        _isLoading = false;
      });
      
      if (kDebugMode) {
        print('📍 Loaded ${_allCities.length} cities for autocomplete');
      }
    } catch (e) {
      setState(() => _isLoading = false);
      if (kDebugMode) {
        print('❌ Failed to load cities: $e');
      }
    }
  }

  void _onTextChanged() {
    final query = _controller.text.trim().toLowerCase();
    
    if (query.isEmpty) {
      setState(() {
        _suggestions = [];
        _showSuggestions = false;
      });
      return;
    }

    // Filter cities based on query
    final filteredCities = _allCities.where((city) {
      return city.toLowerCase().contains(query) ||
             _nlpService.isCitySupported(query);
    }).take(5).toList();

    setState(() {
      _suggestions = filteredCities;
      _showSuggestions = filteredCities.isNotEmpty;
    });
  }

  void _onFocusChanged() {
    if (!_focusNode.hasFocus) {
      setState(() => _showSuggestions = false);
    }
  }

  void _selectCity(String city) {
    _controller.text = city;
    setState(() => _showSuggestions = false);
    _focusNode.unfocus();
    widget.onCitySelected(city);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Text Field
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: _focusNode.hasFocus 
                  ? AppColors.primary 
                  : AppColors.textTertiary.withOpacity(0.3),
              width: _focusNode.hasFocus ? 2 : 1,
            ),
            boxShadow: [
              if (_focusNode.hasFocus)
                BoxShadow(
                  color: AppColors.primary.withOpacity(0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
            ],
          ),
          child: TextField(
            controller: _controller,
            focusNode: _focusNode,
            enabled: widget.enabled,
            textDirection: TextDirection.rtl,
            style: const TextStyle(
              fontSize: 16,
              color: AppColors.textPrimary,
            ),
            decoration: InputDecoration(
              hintText: widget.hintText,
              hintStyle: TextStyle(
                color: AppColors.textTertiary.withOpacity(0.6),
                fontSize: 16,
              ),
              prefixIcon: _isLoading
                  ? const Padding(
                      padding: EdgeInsets.all(12),
                      child: SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                        ),
                      ),
                    )
                  : const Icon(
                      Icons.location_city,
                      color: AppColors.primary,
                    ),
              suffixIcon: _controller.text.isNotEmpty
                  ? IconButton(
                      onPressed: () {
                        _controller.clear();
                        widget.onCitySelected('');
                      },
                      icon: const Icon(
                        Icons.clear,
                        color: AppColors.textTertiary,
                      ),
                    )
                  : null,
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 16,
              ),
            ),
          ),
        ),
        
        // Suggestions Dropdown
        if (_showSuggestions && _suggestions.isNotEmpty)
          Container(
            margin: const EdgeInsets.only(top: 4),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.textTertiary.withOpacity(0.2),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _suggestions.length,
              itemBuilder: (context, index) {
                final city = _suggestions[index];
                return ListTile(
                  dense: true,
                  leading: const Icon(
                    Icons.location_on,
                    color: AppColors.primary,
                    size: 20,
                  ),
                  title: Text(
                    city,
                    style: const TextStyle(
                      fontSize: 14,
                      color: AppColors.textPrimary,
                    ),
                    textDirection: TextDirection.rtl,
                  ),
                  onTap: () => _selectCity(city),
                  hoverColor: AppColors.primary.withOpacity(0.05),
                );
              },
            ),
          ),
      ],
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }
}
