import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:speech_to_text/speech_to_text.dart';
import 'package:flutter/services.dart';
import '../../constants/app_theme.dart';
import '../../services/supabase_service.dart';
import '../../models/trip_model.dart';
import '../../widgets/trip_card.dart';
import '../trip/trip_details_page.dart';
import '../../utils/navigation_utils.dart';

class VoiceAssistantPageV2 extends StatefulWidget {
  const VoiceAssistantPageV2({super.key});

  @override
  State<VoiceAssistantPageV2> createState() => _VoiceAssistantPageV2State();
}

class _VoiceAssistantPageV2State extends State<VoiceAssistantPageV2>
    with TickerProviderStateMixin {
  // Voice recognition
  final SpeechToText _speechToText = SpeechToText();
  bool _speechEnabled = false;
  bool _isListening = false;
  String _recognizedText = '';

  // Audio feedback (using system sounds for now)

  // Trip search
  List<TripModel> _foundTrips = [];
  bool _isSearching = false;
  String _statusMessage = 'مرحبا والف مرحبا، فين بغيت تمشي؟';

  // Animation controllers
  late AnimationController _rippleController;
  late AnimationController _pulseController;
  late Animation<double> _rippleAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeSpeech();
  }

  void _initializeAnimations() {
    _rippleController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _rippleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _rippleController,
      curve: Curves.easeOut,
    ));

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
  }

  Future<void> _initializeSpeech() async {
    try {
      // Check if running on web and show appropriate message
      if (kIsWeb) {
        setState(() {
          _statusMessage = 'التعرف على الصوت متاح على المتصفحات الحديثة';
        });
      }

      _speechEnabled = await _speechToText.initialize(
        onError: (error) {
          setState(() {
            _statusMessage = kIsWeb
                ? 'تأكد من السماح للمتصفح بالوصول للميكروفون'
                : 'حدث خطأ في التعرف على الصوت';
            _isListening = false;
          });
          _stopAnimations();
        },
        onStatus: (status) {
          if (status == 'done' || status == 'notListening') {
            setState(() {
              _isListening = false;
            });
            _stopAnimations();
          }
        },
      );

      if (!_speechEnabled) {
        setState(() {
          _statusMessage = kIsWeb
              ? 'يرجى السماح للمتصفح بالوصول للميكروفون'
              : 'التعرف على الصوت غير متاح';
        });
      }
    } catch (e) {
      setState(() {
        _statusMessage = kIsWeb
            ? 'فشل في الوصول للميكروفون. تحقق من إعدادات المتصفح'
            : 'فشل في تهيئة التعرف على الصوت';
      });
    }
  }

  void _startListening() async {
    if (!_speechEnabled) return;

    setState(() {
      _isListening = true;
      _statusMessage = 'أستمع إليك...';
      _recognizedText = '';
    });

    _startAnimations();

    await _speechToText.listen(
      onResult: (result) {
        setState(() {
          _recognizedText = result.recognizedWords;
        });

        if (result.finalResult) {
          _processVoiceInput(result.recognizedWords);
        }
      },
      listenFor: const Duration(seconds: 10),
      pauseFor: const Duration(seconds: 3),
      listenOptions: SpeechListenOptions(
        partialResults: true,
        onDevice: false,
        listenMode: ListenMode.confirmation,
      ),
      localeId: 'ar-MA', // Moroccan Arabic
    );
  }

  void _stopListening() async {
    await _speechToText.stop();
    setState(() {
      _isListening = false;
    });
    _stopAnimations();
  }

  void _startAnimations() {
    _rippleController.repeat();
    _pulseController.repeat(reverse: true);
  }

  void _stopAnimations() {
    _rippleController.stop();
    _pulseController.stop();
  }

  Future<void> _processVoiceInput(String text) async {
    if (text.trim().isEmpty) return;

    setState(() {
      _statusMessage = 'أبحث عن الرحلات...';
      _isSearching = true;
      _foundTrips = [];
    });

    try {
      // Extract cities from the voice input
      final cities = _extractCities(text);

      if (cities.isEmpty) {
        setState(() {
          _statusMessage = 'لم أتمكن من فهم المدن المطلوبة. حاول مرة أخرى';
          _isSearching = false;
        });
        return;
      }

      // Search for trips
      final allTrips = await SupabaseService.getTrips();
      final filteredTrips = _filterTrips(allTrips, cities);

      setState(() {
        _foundTrips = filteredTrips;
        _isSearching = false;
      });

      if (filteredTrips.isNotEmpty) {
        setState(() {
          _statusMessage = 'آه لقيت ليك ${filteredTrips.length} رحلات، شوفها!';
        });
        _playSuccessSound();
      } else {
        setState(() {
          _statusMessage = 'معليش، ما لقيتش رحلات دابا 😔';
        });
      }
    } catch (e) {
      setState(() {
        _statusMessage = 'حدث خطأ أثناء البحث';
        _isSearching = false;
      });
    }
  }

  List<String> _extractCities(String text) {
    final cities = <String>[];
    final lowerText = text.toLowerCase();

    // City mappings (French/Arabic to Arabic)
    final cityMappings = {
      'agadir': 'أكادير',
      'rabat': 'الرباط',
      'casablanca': 'الدار البيضاء',
      'casa': 'الدار البيضاء',
      'fes': 'فاس',
      'marrakech': 'مراكش',
      'tanger': 'طنجة',
      'meknes': 'مكناس',
      'oujda': 'وجدة',
      'nador': 'الناظور',
    };

    // Check for mapped cities
    for (final entry in cityMappings.entries) {
      if (lowerText.contains(entry.key)) {
        cities.add(entry.value);
      }
    }

    // Check for Arabic cities directly
    final arabicCities = [
      'الدار البيضاء',
      'الرباط',
      'فاس',
      'مراكش',
      'أكادير',
      'طنجة',
      'مكناس',
      'وجدة',
      'تطوان',
      'الناظور',
      'الحسيمة'
    ];

    for (final city in arabicCities) {
      if (text.contains(city)) {
        cities.add(city);
      }
    }

    return cities.toSet().toList(); // Remove duplicates
  }

  List<TripModel> _filterTrips(List<TripModel> trips, List<String> cities) {
    if (cities.isEmpty) return [];

    return trips.where((trip) {
      for (final city in cities) {
        if (trip.fromCity.contains(city) || trip.toCity.contains(city)) {
          return true;
        }
      }
      return false;
    }).toList();
  }

  Future<void> _playSuccessSound() async {
    try {
      // Use system sound for now
      HapticFeedback.lightImpact();
    } catch (e) {
      // Sound error - ignore silently
    }
  }

  @override
  void dispose() {
    _rippleController.dispose();
    _pulseController.dispose();
    _speechToText.stop();
    super.dispose();
  }

  Widget _buildVoiceSection(BoxConstraints constraints) {
    return Container(
      height: constraints.maxHeight * 0.6,
      width: double.infinity,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [AppColors.primary, AppColors.background],
          stops: [0.0, 1.0],
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Animated Voice Circle
          _buildVoiceCircle(),

          const SizedBox(height: 32),

          // Status Message
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: Text(
              _statusMessage,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),

          const SizedBox(height: 16),

          // Recognized Text
          if (_recognizedText.isNotEmpty)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.9),
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Text(
                  _recognizedText,
                  style: const TextStyle(
                    fontSize: 16,
                    color: AppColors.textPrimary,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildVoiceCircle() {
    return GestureDetector(
      onTap: _isListening ? _stopListening : _startListening,
      child: SizedBox(
        width: 200,
        height: 200,
        child: Stack(
          alignment: Alignment.center,
          children: [
            // Ripple effects
            if (_isListening) ...[
              AnimatedBuilder(
                animation: _rippleAnimation,
                builder: (context, child) {
                  return Container(
                    width: 200 + (_rippleAnimation.value * 100),
                    height: 200 + (_rippleAnimation.value * 100),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: Colors.white.withValues(
                          alpha: 1.0 - _rippleAnimation.value,
                        ),
                        width: 2,
                      ),
                    ),
                  );
                },
              ),
              AnimatedBuilder(
                animation: _rippleAnimation,
                builder: (context, child) {
                  final delayedValue =
                      (_rippleAnimation.value - 0.3).clamp(0.0, 1.0);
                  return Container(
                    width: 200 + (delayedValue * 80),
                    height: 200 + (delayedValue * 80),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: Colors.white.withValues(
                          alpha: 1.0 - delayedValue,
                        ),
                        width: 1.5,
                      ),
                    ),
                  );
                },
              ),
            ],

            // Main circle
            AnimatedBuilder(
              animation: _pulseAnimation,
              builder: (context, child) {
                return Transform.scale(
                  scale: _isListening ? _pulseAnimation.value : 1.0,
                  child: Container(
                    width: 160,
                    height: 160,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: RadialGradient(
                        colors: [
                          _isListening ? AppColors.secondary : Colors.white,
                          _isListening
                              ? AppColors.accent
                              : Colors.white.withValues(alpha: 0.8),
                        ],
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: (_isListening
                                  ? AppColors.secondary
                                  : Colors.white)
                              .withValues(alpha: 0.4),
                          blurRadius: 20,
                          spreadRadius: 5,
                        ),
                      ],
                    ),
                    child: Icon(
                      _isListening ? Icons.mic : Icons.mic_none,
                      size: 60,
                      color: _isListening ? Colors.white : AppColors.primary,
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResultsSection() {
    if (_isSearching) {
      return Container(
        padding: const EdgeInsets.all(32),
        child: const Column(
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text(
              'جاري البحث عن الرحلات...',
              style: TextStyle(
                fontSize: 16,
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      );
    }

    if (_foundTrips.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(32),
        child: const Column(
          children: [
            Icon(
              Icons.search,
              size: 64,
              color: AppColors.textTertiary,
            ),
            SizedBox(height: 16),
            Text(
              'اضغط على الميكروفون وقل وجهتك',
              style: TextStyle(
                fontSize: 16,
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الرحلات المتاحة (${_foundTrips.length})',
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 16),
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _foundTrips.length,
            itemBuilder: (context, index) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: TripCard(
                  trip: _foundTrips[index],
                  onTap: () {
                    _navigateToTripDetails(context, _foundTrips[index]);
                  },
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('المساعد الصوتي'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SafeArea(
        child: LayoutBuilder(
          builder: (context, constraints) {
            return SingleChildScrollView(
              physics: const ClampingScrollPhysics(),
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  minHeight: constraints.maxHeight,
                ),
                child: Column(
                  children: [
                    // Voice Assistant Section
                    _buildVoiceSection(constraints),

                    // Results Section
                    _buildResultsSection(),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  /// Navigate to trip details page with null safety
  void _navigateToTripDetails(BuildContext context, TripModel trip) {
    // Ensure trip has a valid ID before navigation
    if (trip.id.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('خطأ: معرف الرحلة غير صحيح'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    try {
      NavigationUtils.pushWithTransition(
        context,
        TripDetailsPage(tripId: trip.id),
        type: TransitionType.slide,
        duration: const Duration(milliseconds: 400),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('خطأ في التنقل إلى تفاصيل الرحلة'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
