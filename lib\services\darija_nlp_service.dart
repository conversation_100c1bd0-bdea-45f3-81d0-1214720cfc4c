import 'package:flutter/foundation.dart';

class TripQuery {
  final String? fromCity;
  final String? toCity;
  final String originalText;
  final double confidence;

  TripQuery({
    this.fromCity,
    this.toCity,
    required this.originalText,
    this.confidence = 0.0,
  });

  bool get isValid => fromCity != null && toCity != null;

  @override
  String toString() {
    return 'TripQuery(from: $fromCity, to: $toCity, confidence: $confidence)';
  }
}

class DarijaNLPService {
  static final DarijaNLPService _instance = DarijaNLPService._internal();
  factory DarijaNLPService() => _instance;
  DarijaNLPService._internal();

  // Moroccan cities with their common variations and pronunciations
  static const Map<String, List<String>> _moroccanCities = {
    'الدار البيضاء': [
      'الدار البيضاء',
      'كازابلانكا',
      'الدار',
      'casa',
      'casablanca',
      'كازا',
      'الكازا'
    ],
    'الرباط': ['الرباط', 'rabat', 'رباط'],
    'فاس': ['فاس', 'fes', 'fez'],
    'مراكش': ['مراكش', 'marrakech', 'marrakesh', 'مراكش الحمراء'],
    'أكادير': ['أكادير', 'agadir'],
    'طنجة': ['طنجة', 'tanger', 'tangier', 'تانجا'],
    'مكناس': ['مكناس', 'meknes'],
    'وجدة': ['وجدة', 'oujda'],
    'تطوان': ['تطوان', 'tetouan'],
    'الناظور': ['الناظور', 'nador'],
    'الحسيمة': ['الحسيمة', 'al hoceima', 'الحسيمة'],
    'بني ملال': ['بني ملال', 'beni mellal'],
    'خريبكة': ['خريبكة', 'khouribga'],
    'الجديدة': ['الجديدة', 'el jadida'],
    'سطات': ['سطات', 'settat'],
    'تازة': ['تازة', 'taza'],
    'ورزازات': ['ورزازات', 'ouarzazate'],
    'الصويرة': ['الصويرة', 'essaouira'],
    'القنيطرة': ['القنيطرة', 'kenitra'],
    'سلا': ['سلا', 'sale'],
    'تمارة': ['تمارة', 'temara'],
    'المحمدية': ['المحمدية', 'mohammedia'],
    'برشيد': ['برشيد', 'berrechid'],
    'الخميسات': ['الخميسات', 'khemisset'],
    'إفران': ['إفران', 'ifrane'],
    'الراشيدية': ['الراشيدية', 'errachidia'],
    'زاكورة': ['زاكورة', 'zagora'],
    'تارودانت': ['تارودانت', 'taroudant'],
    'تيزنيت': ['تيزنيت', 'tiznit'],
    'العيون': ['العيون', 'laayoune'],
    'الداخلة': ['الداخلة', 'dakhla'],
  };

  // Natural Darija responses for different scenarios
  static const List<String> _greetingResponses = [
    'مرحبا ألف، فين بغيتي تمشي؟ أنا هنا نعاونك 😊',
    'أهلا وسهلا! قولي ليا فين بغيتي تروحي؟',
    'السلام عليكم! واش بغيتي تسافري؟ قولي ليا',
    'مرحبا بيك! أنا المساعد ديالك، فين بغيتي نوصلك؟',
  ];

  static const List<String> _successResponses = [
    'آه! فهمت عليك، بغيتي تمشي من {from} ل {to}. دابا نشوف ليك الرحلات 🚗',
    'زوين! من {from} ل {to}، غادي نلقى ليك أحسن رحلة 👌',
    'واخا! {from} ل {to}، دابا نبحث ليك على الرحلات المتاحة',
    'مزيان! فهمت بغيتي تمشي من {from} ل {to}، استنى شوية',
  ];

  static const List<String> _foundTripsResponses = [
    'آه! لقيت ليك {count} رحلة زوينة، تفرّج عليها! 👇',
    'مبروك! كاين {count} رحلات متاحة، شوف أيهما يعجبك',
    'زوين! لقيت {count} رحلات، اختار اللي بغيتي',
    'ممتاز! عندك {count} رحلات للاختيار منها',
  ];

  static const List<String> _noTripsResponses = [
    'ما كيناش الرحلة دابا، ولكن غادي نخلي عيني عليها 😉',
    'للأسف ما لقيناش رحلات دابا، جرب مرة أخرى بعد شوية',
    'ما كاينش رحلات متاحة الوقت، ولكن ممكن تجرب مرة ثانية',
    'حاليا ما عندناش رحلات، ولكن غادي نبلغك إيلا جات وحدة',
  ];

  static const List<String> _errorResponses = [
    'سمح ليا، ما فهمتش مزيان. قول ليا مرة أخرى فين بغيتي تمشي؟',
    'معذرة، ما قدرتش نفهم. واش ممكن تعاود؟',
    'آسف، ما وصلنيش الكلام مزيان. عاود قول ليا',
    'سمح ليا، ممكن تقول ليا مرة ثانية بوضوح؟',
  ];

  // Common Darija patterns for expressing travel intentions
  static const List<String> _travelPatterns = [
    // "I want to go from X to Y"
    r'بغيت\s+نمشي\s+من\s+(.+?)\s+ل\s*(.+)',
    r'بغيت\s+نسافر\s+من\s+(.+?)\s+ل\s*(.+)',
    r'بغيت\s+نروح\s+من\s+(.+?)\s+ل\s*(.+)',

    // "I want to go to Y from X"
    r'بغيت\s+نمشي\s+ل\s*(.+?)\s+من\s+(.+)',
    r'بغيت\s+نسافر\s+ل\s*(.+?)\s+من\s+(.+)',
    r'بغيت\s+نروح\s+ل\s*(.+?)\s+من\s+(.+)',

    // "From X to Y"
    r'من\s+(.+?)\s+ل\s*(.+)',
    r'من\s+(.+?)\s+إلى\s+(.+)',

    // English patterns
    r'from\s+(.+?)\s+to\s+(.+)',
    r'i\s+want\s+to\s+go\s+from\s+(.+?)\s+to\s+(.+)',
  ];

  /// Parse a Darija text query to extract trip information
  TripQuery parseQuery(String text) {
    if (text.trim().isEmpty) {
      return TripQuery(originalText: text);
    }

    final normalizedText = _normalizeText(text);

    if (kDebugMode) {
      print('🔍 Parsing query: "$text"');
      print('📝 Normalized: "$normalizedText"');
    }

    // Try to match travel patterns
    for (final pattern in _travelPatterns) {
      final regex = RegExp(pattern, caseSensitive: false, unicode: true);
      final match = regex.firstMatch(normalizedText);

      if (match != null && match.groupCount >= 2) {
        String? fromCity, toCity;

        // Determine if pattern is "from X to Y" or "to Y from X"
        if (pattern.contains(r'ل\s*(.+?)\s+من') ||
            pattern.contains(r'to\s+(.+?)\s+from')) {
          // Pattern: "to Y from X"
          toCity = _extractCity(match.group(1)?.trim() ?? '');
          fromCity = _extractCity(match.group(2)?.trim() ?? '');
        } else {
          // Pattern: "from X to Y"
          fromCity = _extractCity(match.group(1)?.trim() ?? '');
          toCity = _extractCity(match.group(2)?.trim() ?? '');
        }

        final confidence =
            _calculateConfidence(fromCity, toCity, normalizedText);

        final result = TripQuery(
          fromCity: fromCity,
          toCity: toCity,
          originalText: text,
          confidence: confidence,
        );

        if (kDebugMode) {
          print('✅ Parsed result: $result');
        }

        return result;
      }
    }

    // Fallback: try to extract any two cities mentioned
    final cities = _extractAllCities(normalizedText);
    if (cities.length >= 2) {
      final result = TripQuery(
        fromCity: cities[0],
        toCity: cities[1],
        originalText: text,
        confidence: 0.5, // Lower confidence for fallback
      );

      if (kDebugMode) {
        print('🔄 Fallback result: $result');
      }

      return result;
    }

    if (kDebugMode) {
      print('❌ Could not parse query: "$text"');
    }

    return TripQuery(originalText: text);
  }

  /// Normalize text for better matching
  String _normalizeText(String text) {
    return text
        .toLowerCase()
        .trim()
        .replaceAll(RegExp(r'\s+'), ' ') // Multiple spaces to single space
        .replaceAll('إلى', 'ل') // Normalize "to" variations
        .replaceAll('الى', 'ل')
        .replaceAll('لل', 'ل'); // Fix double "ل"
  }

  /// Extract a city name from text
  String? _extractCity(String text) {
    if (text.trim().isEmpty) return null;

    final normalizedText = text.toLowerCase().trim();

    // Direct match with city names
    for (final entry in _moroccanCities.entries) {
      final cityName = entry.key;
      final variations = entry.value;

      for (final variation in variations) {
        if (normalizedText.contains(variation.toLowerCase())) {
          return cityName; // Return the standard Arabic name
        }
      }
    }

    // If no direct match, return the cleaned text (might be a city not in our list)
    final cleaned = text
        .trim()
        .replaceAll(RegExp(r'[^\u0600-\u06FF\u0750-\u077F\w\s]'), '');
    return cleaned.isNotEmpty ? cleaned : null;
  }

  /// Extract all cities mentioned in the text
  List<String> _extractAllCities(String text) {
    final cities = <String>[];
    final words = text.split(' ');

    for (int i = 0; i < words.length; i++) {
      // Try single word
      final singleWord = _extractCity(words[i]);
      if (singleWord != null && !cities.contains(singleWord)) {
        cities.add(singleWord);
      }

      // Try two words combination
      if (i < words.length - 1) {
        final twoWords = '${words[i]} ${words[i + 1]}';
        final twoWordCity = _extractCity(twoWords);
        if (twoWordCity != null && !cities.contains(twoWordCity)) {
          cities.add(twoWordCity);
        }
      }
    }

    return cities;
  }

  /// Calculate confidence score for the parsed result
  double _calculateConfidence(String? fromCity, String? toCity, String text) {
    if (fromCity == null || toCity == null) return 0.0;

    double confidence = 0.5; // Base confidence

    // Boost confidence if both cities are in our known list
    if (_moroccanCities.containsKey(fromCity)) confidence += 0.2;
    if (_moroccanCities.containsKey(toCity)) confidence += 0.2;

    // Boost confidence if travel keywords are present
    if (text.contains('بغيت') || text.contains('want')) confidence += 0.1;
    if (text.contains('نمشي') ||
        text.contains('نسافر') ||
        text.contains('نروح')) confidence += 0.1;

    return confidence.clamp(0.0, 1.0);
  }

  /// Get all supported Moroccan cities
  List<String> getSupportedCities() {
    return _moroccanCities.keys.toList();
  }

  /// Check if a city is supported
  bool isCitySupported(String city) {
    return _moroccanCities.containsKey(city) ||
        _moroccanCities.values.any((variations) => variations
            .any((variation) => variation.toLowerCase() == city.toLowerCase()));
  }

  /// Generate a greeting response
  String generateGreetingResponse() {
    final responses = _greetingResponses;
    return responses[
        (DateTime.now().millisecondsSinceEpoch % responses.length)];
  }

  /// Generate a response message for successful parsing
  String generateSuccessResponse(TripQuery query) {
    if (!query.isValid) return generateErrorResponse();

    final responses = _successResponses;
    final selectedResponse =
        responses[(DateTime.now().millisecondsSinceEpoch % responses.length)];

    return selectedResponse
        .replaceAll('{from}', query.fromCity!)
        .replaceAll('{to}', query.toCity!);
  }

  /// Generate response for found trips
  String generateFoundTripsResponse(int tripCount) {
    final responses = _foundTripsResponses;
    final selectedResponse =
        responses[(DateTime.now().millisecondsSinceEpoch % responses.length)];

    String countText;
    if (tripCount == 1) {
      countText = 'رحلة وحدة';
    } else if (tripCount == 2) {
      countText = 'جوج رحلات';
    } else {
      countText = '$tripCount رحلات';
    }

    return selectedResponse.replaceAll('{count}', countText);
  }

  /// Generate response for no trips found
  String generateNoTripsResponse() {
    final responses = _noTripsResponses;
    return responses[
        (DateTime.now().millisecondsSinceEpoch % responses.length)];
  }

  /// Generate a fallback response for failed parsing
  String generateErrorResponse() {
    final responses = _errorResponses;
    return responses[
        (DateTime.now().millisecondsSinceEpoch % responses.length)];
  }

  /// Generate a fallback response for failed parsing (legacy method)
  String generateFallbackResponse() {
    return generateErrorResponse();
  }
}
