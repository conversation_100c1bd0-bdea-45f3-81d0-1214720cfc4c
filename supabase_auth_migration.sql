-- Migration to Supa<PERSON> Auth for <PERSON><PERSON><PERSON>
-- This script migrates from custom authentication to Supabase Auth

-- Drop the existing custom users table
DROP TABLE IF EXISTS public.users CASCADE;

-- Create new users table that extends auth.users
CREATE TABLE public.users (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    phone TEXT UNIQUE NOT NULL,
    full_name TEXT NOT NULL,
    role TEXT NOT NULL DEFAULT 'traveler' CHECK (role IN ('traveler', 'trip_leader')),
    email TEXT, -- Optional field for future email verification
    profile_image_url TEXT,
    bio TEXT,
    city TEXT,
    date_of_birth DATE,
    gender TEXT CHECK (gender IN ('male', 'female')),
    is_verified BOOLEAN DEFAULT FALSE,
    is_leader BOOLEAN DEFAULT FALSE,
    balance DECIMAL(10,2) DEFAULT 0.00,
    rating DECIMAL(3,2) DEFAULT 0.00,
    total_trips INTEGER DEFAULT 0,
    total_ratings INTEGER DEFAULT 0,
    badges TEXT[] DEFAULT '{}',
    preferences JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on users table
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;

-- Policy: Allow users to read their own profile and other users' public info
CREATE POLICY "Users can view profiles" ON public.users
    FOR SELECT USING (true);

-- Policy: Allow users to update their own profile
CREATE POLICY "Users can update own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

-- Function to automatically create user profile on signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.users (id, phone, full_name)
    VALUES (
        NEW.id, 
        COALESCE(NEW.phone, NEW.raw_user_meta_data->>'phone', ''),
        COALESCE(NEW.raw_user_meta_data->>'full_name', 'مستخدم جديد')
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for new user creation
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Update updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Note: After running this migration, you'll need to:
-- 1. Enable phone authentication in Supabase Auth settings
-- 2. Configure phone providers (Twilio, etc.) if needed for production
-- 3. Test signup and login with phone numbers in +212XXXXXXXXX format
