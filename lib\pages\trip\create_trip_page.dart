import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_theme.dart';
import '../../generated/l10n.dart';
import '../../models/trip_model.dart';
import '../../providers/trip_provider.dart';

class CreateTripPage extends StatelessWidget {
  const CreateTripPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(S.of(context).createTrip),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.add_circle, size: 64, color: AppColors.primary),
            SizedBox(height: 16),
            Text('إنشاء رحلة جديدة'),
            SizedBox(height: 8),
            Text('هذه الصفحة قيد التطوير'),
          ],
        ),
      ),
    );
  }
}

// EditTripPage - Proper implementation with pre-filled data
class EditTripPage extends StatefulWidget {
  final String tripId;
  const EditTripPage({super.key, required this.tripId});

  @override
  State<EditTripPage> createState() => _EditTripPageState();
}

class _EditTripPageState extends State<EditTripPage> {
  TripModel? _trip;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadTripData();
  }

  Future<void> _loadTripData() async {
    try {
      final tripProvider = Provider.of<TripProvider>(context, listen: false);
      await tripProvider.loadTripById(widget.tripId);

      setState(() {
        _trip = tripProvider.selectedTrip;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'فشل في تحميل بيانات الرحلة';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تعديل الرحلة'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: AppColors.error,
            ),
            const SizedBox(height: 16),
            Text(
              _error!,
              style: const TextStyle(
                fontSize: 16,
                color: AppColors.error,
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadTripData,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    if (_trip == null) {
      return const Center(
        child: Text('لم يتم العثور على الرحلة'),
      );
    }

    // For now, show trip details and a placeholder for editing
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'تفاصيل الرحلة الحالية',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 16),
                  _buildTripDetail('من', _trip!.fromCity),
                  _buildTripDetail('إلى', _trip!.toCity),
                  _buildTripDetail('التاريخ',
                      '${_trip!.departureDate.day}/${_trip!.departureDate.month}/${_trip!.departureDate.year}'),
                  _buildTripDetail('الوقت', _trip!.departureTime),
                  _buildTripDetail(
                      'السعر', '${_trip!.price.toStringAsFixed(0)} درهم'),
                  _buildTripDetail('المقاعد', '${_trip!.totalSeats}'),
                  if (_trip!.carModel != null)
                    _buildTripDetail('السيارة', _trip!.carModel!),
                  if (_trip!.carPlate != null)
                    _buildTripDetail('اللوحة', _trip!.carPlate!),
                ],
              ),
            ),
          ),
          const SizedBox(height: 24),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.info.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppColors.info.withOpacity(0.3)),
            ),
            child: Column(
              children: [
                Icon(
                  Icons.construction,
                  size: 48,
                  color: AppColors.info,
                ),
                const SizedBox(height: 8),
                const Text(
                  'صفحة التعديل قيد التطوير',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                const Text(
                  'سيتم إضافة إمكانية تعديل تفاصيل الرحلة قريباً',
                  style: TextStyle(fontSize: 14),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTripDetail(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                color: AppColors.textPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class BookingPage extends StatelessWidget {
  final String tripId;
  const BookingPage({super.key, required this.tripId});
  @override
  Widget build(BuildContext context) => Scaffold(
        appBar: AppBar(title: const Text('حجز الرحلة')),
        body: Center(child: Text('حجز الرحلة: $tripId')),
      );
}

class BookingDetailsPage extends StatelessWidget {
  final String bookingId;
  const BookingDetailsPage({super.key, required this.bookingId});
  @override
  Widget build(BuildContext context) => Scaffold(
        appBar: AppBar(title: const Text('تفاصيل الحجز')),
        body: Center(child: Text('تفاصيل الحجز: $bookingId')),
      );
}

class ProfilePage extends StatelessWidget {
  final String? userId;
  const ProfilePage({super.key, this.userId});
  @override
  Widget build(BuildContext context) => Scaffold(
        appBar: AppBar(title: const Text('الملف الشخصي')),
        body: Center(child: Text('الملف الشخصي: ${userId ?? 'الحالي'}')),
      );
}

class EditProfilePage extends StatelessWidget {
  const EditProfilePage({super.key});
  @override
  Widget build(BuildContext context) => Scaffold(
        appBar: AppBar(title: const Text('تعديل الملف الشخصي')),
        body: const Center(child: Text('تعديل الملف الشخصي')),
      );
}

class ConversationsPage extends StatelessWidget {
  const ConversationsPage({super.key});
  @override
  Widget build(BuildContext context) => Scaffold(
        appBar: AppBar(title: const Text('المحادثات')),
        body: const Center(child: Text('المحادثات')),
      );
}

class ChatPage extends StatelessWidget {
  final String conversationId;
  const ChatPage({super.key, required this.conversationId});
  @override
  Widget build(BuildContext context) => Scaffold(
        appBar: AppBar(title: const Text('المحادثة')),
        body: Center(child: Text('المحادثة: $conversationId')),
      );
}

class RatingPage extends StatelessWidget {
  final String tripId;
  final String userId;
  const RatingPage({super.key, required this.tripId, required this.userId});
  @override
  Widget build(BuildContext context) => Scaffold(
        appBar: AppBar(title: const Text('التقييم')),
        body: Center(child: Text('تقييم المستخدم: $userId للرحلة: $tripId')),
      );
}

class SearchPage extends StatelessWidget {
  const SearchPage({super.key});
  @override
  Widget build(BuildContext context) => Scaffold(
        appBar: AppBar(title: const Text('البحث')),
        body: const Center(child: Text('البحث عن الرحلات')),
      );
}

class LeaderDashboardPage extends StatelessWidget {
  const LeaderDashboardPage({super.key});
  @override
  Widget build(BuildContext context) => Scaffold(
        appBar: AppBar(title: const Text('لوحة قائد الرحلة')),
        body: const Center(child: Text('لوحة قائد الرحلة')),
      );
}

class TravelerDashboardPage extends StatelessWidget {
  const TravelerDashboardPage({super.key});
  @override
  Widget build(BuildContext context) => Scaffold(
        appBar: AppBar(title: const Text('لوحة المسافر')),
        body: const Center(child: Text('لوحة المسافر')),
      );
}
