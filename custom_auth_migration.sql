-- Custom Authentication Migration for <PERSON><PERSON><PERSON>
-- This script migrates from Supabase Auth to custom authentication

-- Drop the existing users table that depends on auth.users
DROP TABLE IF EXISTS public.users CASCADE;

-- Create new custom users table (standalone, no Supabase auth dependency)
CREATE TABLE public.users (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    phone TEXT UNIQUE NOT NULL,
    full_name TEXT NOT NULL,
    password TEXT NOT NULL, -- Plain text for now, will be hashed later
    role TEXT NOT NULL DEFAULT 'traveler' CHECK (role IN ('traveler', 'trip_leader')),
    email TEXT, -- Optional field for future email verification
    profile_image_url TEXT,
    bio TEXT,
    city TEXT,
    date_of_birth DATE,
    gender TEXT CHECK (gender IN ('male', 'female')),
    is_verified BOOLEAN DEFAULT FALSE,
    is_leader BOOLEAN DEFAULT FALSE,
    balance DECIMAL(10,2) DEFAULT 0.00,
    rating DECIMAL(3,2) DEFAULT 0.00,
    total_trips INTEGER DEFAULT 0,
    total_ratings INTEGER DEFAULT 0,
    badges TEXT[] DEFAULT '{}',
    preferences JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Row Level Security (RLS) Policies for custom authentication

-- Enable RLS on users table
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;

-- Policy: Allow public read access for user profiles (needed for app functionality)
CREATE POLICY "Allow public read access to users" ON public.users
    FOR SELECT USING (true);

-- Policy: Allow users to insert their own profile during signup
CREATE POLICY "Allow public insert for signup" ON public.users
    FOR INSERT WITH CHECK (true);

-- Policy: Allow users to update their own profile
CREATE POLICY "Allow users to update own profile" ON public.users
    FOR UPDATE USING (true) WITH CHECK (true);

-- Note: For custom auth, we handle authentication in the application layer
-- These policies allow the necessary database operations for our custom auth system

-- Create some test users for development
INSERT INTO public.users (phone, full_name, password, role) VALUES
('*********', 'أحمد محمد', '123456', 'traveler'),
('*********', 'فاطمة علي', '123456', 'trip_leader'),
('*********', 'محمد حسن', '123456', 'traveler');

-- Update other tables to reference the new users table structure
-- Note: This assumes the trips and other tables will be updated to work with the new user IDs
