import 'user_model.dart';

class TripModel {
  final String id;
  final String leaderId;
  final UserModel? leader;
  final String title;
  final String description;
  final String fromCity;
  final String toCity;
  final DateTime departureDate;
  final DateTime? returnDate;
  final String departureTime;
  final String? returnTime;
  final double price;
  final int totalSeats;
  final int availableSeats;
  final String tripType; // 'mixed', 'women_only', 'family_only'
  final String status; // 'active', 'completed', 'cancelled', 'published'
  final List<String> imageUrls;
  final String? carModel;
  final String? carColor;
  final String? carPlateNumber;
  final List<String> rules;
  final List<TripStop> stops;
  final Map<String, dynamic>? meetingPoint;
  final String? notes;
  final bool allowInstantBooking;
  final DateTime? bookingDeadline;
  final double rating;
  final int totalRatings;
  final List<String> amenities;
  final DateTime createdAt;
  final DateTime updatedAt;

  // Additional fields for enhanced functionality
  final String? carType;
  final String? carPlate;
  final bool isPriceNegotiable;
  final bool isInstantBooking;
  final int? durationMinutes;
  final String? tripProgram;
  final String? carImage;
  final String? routeFrom;
  final String? routeTo;
  final DateTime? dateTime;

  TripModel({
    required this.id,
    required this.leaderId,
    this.leader,
    required this.title,
    required this.description,
    required this.fromCity,
    required this.toCity,
    required this.departureDate,
    this.returnDate,
    required this.departureTime,
    this.returnTime,
    required this.price,
    required this.totalSeats,
    required this.availableSeats,
    this.tripType = 'mixed',
    this.status = 'active',
    this.imageUrls = const [],
    this.carModel,
    this.carColor,
    this.carPlateNumber,
    this.rules = const [],
    this.stops = const [],
    this.meetingPoint,
    this.notes,
    this.allowInstantBooking = false,
    this.bookingDeadline,
    this.rating = 0.0,
    this.totalRatings = 0,
    this.amenities = const [],
    required this.createdAt,
    required this.updatedAt,
    // New fields
    this.carType,
    this.carPlate,
    this.isPriceNegotiable = false,
    this.isInstantBooking = false,
    this.durationMinutes,
    this.tripProgram,
    this.carImage,
    this.routeFrom,
    this.routeTo,
    this.dateTime,
  });

  factory TripModel.fromJson(Map<String, dynamic> json) {
    return TripModel(
      id: json['id'] as String,
      leaderId: json['leader_id'] as String,
      leader: json['leader'] != null
          ? UserModel.fromJson(json['leader'] as Map<String, dynamic>)
          : null,
      title: json['title'] as String? ?? json['description'] as String? ?? '',
      description: json['description'] as String? ??
          json['trip_program'] as String? ??
          '',
      fromCity:
          json['from_city'] as String? ?? json['route_from'] as String? ?? '',
      toCity: json['to_city'] as String? ?? json['route_to'] as String? ?? '',
      departureDate: json['departure_date'] != null
          ? DateTime.parse(json['departure_date'] as String)
          : json['date_time'] != null
              ? DateTime.parse(json['date_time'] as String)
              : DateTime.now(),
      returnDate: json['return_date'] != null
          ? DateTime.parse(json['return_date'] as String)
          : null,
      departureTime: _parseTimeString(json['departure_time']) ?? '00:00',
      returnTime: _parseTimeString(json['return_time']),
      price: (json['price'] as num?)?.toDouble() ?? 0.0,
      totalSeats:
          json['total_seats'] as int? ?? json['available_seats'] as int? ?? 1,
      availableSeats:
          json['available_seats'] as int? ?? json['total_seats'] as int? ?? 1,
      tripType: json['trip_type'] as String? ?? 'mixed',
      status: json['status'] as String? ?? 'active',
      imageUrls: json['image_urls'] != null
          ? List<String>.from(json['image_urls'] as List)
          : json['car_image'] != null
              ? [json['car_image'] as String]
              : [],
      carModel: json['car_model'] as String? ?? json['car_type'] as String?,
      carColor: json['car_color'] as String?,
      carPlateNumber:
          json['car_plate_number'] as String? ?? json['car_plate'] as String?,
      rules:
          json['rules'] != null ? List<String>.from(json['rules'] as List) : [],
      stops: json['stops'] != null
          ? (json['stops'] as List)
              .map((stop) => TripStop.fromJson(stop))
              .toList()
          : [],
      meetingPoint: json['meeting_point'] as Map<String, dynamic>?,
      notes: json['notes'] as String?,
      allowInstantBooking: json['allow_instant_booking'] as bool? ??
          json['is_instant_booking'] as bool? ??
          false,
      bookingDeadline: json['booking_deadline'] != null
          ? DateTime.parse(json['booking_deadline'] as String)
          : null,
      rating: (json['rating'] as num?)?.toDouble() ?? 0.0,
      totalRatings: json['total_ratings'] as int? ?? 0,
      amenities: json['amenities'] != null
          ? (json['amenities'] is List
              ? List<String>.from(json['amenities'] as List)
              : List<String>.from((json['amenities'] as List).cast<String>()))
          : [],
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'] as String)
          : DateTime.now(),
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'] as String)
          : DateTime.now(),
      // New fields
      carType: json['car_type'] as String?,
      carPlate: json['car_plate'] as String?,
      isPriceNegotiable: json['is_price_negotiable'] as bool? ?? false,
      isInstantBooking: json['is_instant_booking'] as bool? ?? false,
      durationMinutes: json['duration_minutes'] as int?,
      tripProgram: json['trip_program'] as String?,
      carImage: json['car_image'] as String?,
      routeFrom: json['route_from'] as String?,
      routeTo: json['route_to'] as String?,
      dateTime: json['date_time'] != null
          ? DateTime.parse(json['date_time'] as String)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id.isEmpty ? null : id,
      'leader_id': leaderId,
      'driver_id': leaderId, // Add driver_id mapping for RLS compatibility
      'title': title,
      'description': description,
      'from_city': fromCity,
      'to_city': toCity,
      'departure_date':
          departureDate.toIso8601String().split('T')[0], // Date only
      'return_date': returnDate?.toIso8601String().split('T')[0],
      'departure_time': departureTime,
      'return_time': returnTime,
      'price': price,
      'total_seats': totalSeats,
      'available_seats': availableSeats,
      'trip_type': tripType,
      'status': status,
      'image_urls': imageUrls,
      'car_model': carModel ?? carType,
      'car_color': carColor,
      'car_plate_number': carPlateNumber ?? carPlate,
      'rules': rules,
      'stops': stops.map((stop) => stop.toJson()).toList(),
      'meeting_point': meetingPoint,
      'notes': notes,
      'allow_instant_booking': allowInstantBooking,
      'booking_deadline': bookingDeadline?.toIso8601String(),
      'rating': rating,
      'total_ratings': totalRatings,
      'amenities': amenities,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      // New fields
      'car_type': carType,
      'car_plate': carPlate,
      'is_price_negotiable': isPriceNegotiable,
      'is_instant_booking': isInstantBooking,
      'duration_minutes': durationMinutes,
      'trip_program': tripProgram,
      'car_image': carImage,
      'route_from': routeFrom,
      'route_to': routeTo,
      'date_time': dateTime?.toIso8601String(),
    };
  }

  TripModel copyWith({
    String? id,
    String? leaderId,
    UserModel? leader,
    String? title,
    String? description,
    String? fromCity,
    String? toCity,
    DateTime? departureDate,
    DateTime? returnDate,
    String? departureTime,
    String? returnTime,
    double? price,
    int? totalSeats,
    int? availableSeats,
    String? tripType,
    String? status,
    List<String>? imageUrls,
    String? carModel,
    String? carColor,
    String? carPlateNumber,
    List<String>? rules,
    List<TripStop>? stops,
    Map<String, dynamic>? meetingPoint,
    String? notes,
    bool? allowInstantBooking,
    DateTime? bookingDeadline,
    double? rating,
    int? totalRatings,
    List<String>? amenities,
    DateTime? createdAt,
    DateTime? updatedAt,
    // New fields
    String? carType,
    String? carPlate,
    bool? isPriceNegotiable,
    bool? isInstantBooking,
    int? durationMinutes,
    String? tripProgram,
    String? carImage,
    String? routeFrom,
    String? routeTo,
    DateTime? dateTime,
  }) {
    return TripModel(
      id: id ?? this.id,
      leaderId: leaderId ?? this.leaderId,
      leader: leader ?? this.leader,
      title: title ?? this.title,
      description: description ?? this.description,
      fromCity: fromCity ?? this.fromCity,
      toCity: toCity ?? this.toCity,
      departureDate: departureDate ?? this.departureDate,
      returnDate: returnDate ?? this.returnDate,
      departureTime: departureTime ?? this.departureTime,
      returnTime: returnTime ?? this.returnTime,
      price: price ?? this.price,
      totalSeats: totalSeats ?? this.totalSeats,
      availableSeats: availableSeats ?? this.availableSeats,
      tripType: tripType ?? this.tripType,
      status: status ?? this.status,
      imageUrls: imageUrls ?? this.imageUrls,
      carModel: carModel ?? this.carModel,
      carColor: carColor ?? this.carColor,
      carPlateNumber: carPlateNumber ?? this.carPlateNumber,
      rules: rules ?? this.rules,
      stops: stops ?? this.stops,
      meetingPoint: meetingPoint ?? this.meetingPoint,
      notes: notes ?? this.notes,
      allowInstantBooking: allowInstantBooking ?? this.allowInstantBooking,
      bookingDeadline: bookingDeadline ?? this.bookingDeadline,
      rating: rating ?? this.rating,
      totalRatings: totalRatings ?? this.totalRatings,
      amenities: amenities ?? this.amenities,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      // New fields
      carType: carType ?? this.carType,
      carPlate: carPlate ?? this.carPlate,
      isPriceNegotiable: isPriceNegotiable ?? this.isPriceNegotiable,
      isInstantBooking: isInstantBooking ?? this.isInstantBooking,
      durationMinutes: durationMinutes ?? this.durationMinutes,
      tripProgram: tripProgram ?? this.tripProgram,
      carImage: carImage ?? this.carImage,
      routeFrom: routeFrom ?? this.routeFrom,
      routeTo: routeTo ?? this.routeTo,
      dateTime: dateTime ?? this.dateTime,
    );
  }

  bool get isActive => status == 'active';
  bool get isCompleted => status == 'completed';
  bool get isCancelled => status == 'cancelled';
  bool get hasAvailableSeats => availableSeats > 0;
  bool get isBookingOpen =>
      bookingDeadline == null || DateTime.now().isBefore(bookingDeadline!);

  String get displayRating => rating > 0 ? rating.toStringAsFixed(1) : 'جديد';
  String get route => '$fromCity ← $toCity';

  int get bookedSeats => totalSeats - availableSeats;

  // Duration formatting
  String get formattedDuration {
    if (durationMinutes == null || durationMinutes == 0) return '';

    final totalMinutes = durationMinutes!;
    final days = totalMinutes ~/ (24 * 60);
    final hours = (totalMinutes % (24 * 60)) ~/ 60;
    final minutes = totalMinutes % 60;

    List<String> parts = [];
    if (days > 0) parts.add('${days}d');
    if (hours > 0) parts.add('${hours}h');
    if (minutes > 0) parts.add('${minutes}min');

    return parts.join(' ');
  }

  // Helper method to parse time strings from Supabase
  static String? _parseTimeString(dynamic timeValue) {
    if (timeValue == null) return null;

    if (timeValue is String) {
      // If it's already a string, return as is
      if (timeValue.contains(':')) {
        return timeValue;
      }
    }

    // Handle other formats if needed
    return timeValue.toString();
  }
}

class TripStop {
  final String name;
  final String? description;
  final DateTime estimatedTime;
  final int durationMinutes;
  final Map<String, dynamic>? location;

  TripStop({
    required this.name,
    this.description,
    required this.estimatedTime,
    this.durationMinutes = 15,
    this.location,
  });

  factory TripStop.fromJson(Map<String, dynamic> json) {
    return TripStop(
      name: json['name'] as String,
      description: json['description'] as String?,
      estimatedTime: DateTime.parse(json['estimated_time'] as String),
      durationMinutes: json['duration_minutes'] as int? ?? 15,
      location: json['location'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'description': description,
      'estimated_time': estimatedTime.toIso8601String(),
      'duration_minutes': durationMinutes,
      'location': location,
    };
  }
}
