import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class RegistrationService {
  static final SupabaseClient _client = Supabase.instance.client;

  /// Helper method to normalize phone number
  static String _normalizePhone(String phone) {
    // Remove all non-digit characters
    final cleanPhone = phone.replaceAll(RegExp(r'[^\d]'), '');

    // Convert to consistent format: remove leading +212 or 0
    String normalizedPhone = cleanPhone;
    if (normalizedPhone.startsWith('212')) {
      normalizedPhone = normalizedPhone.substring(3);
    } else if (normalizedPhone.startsWith('0')) {
      normalizedPhone = normalizedPhone.substring(1);
    }

    return normalizedPhone;
  }

  /// Register user with phone number and password
  /// Uses email-based <NAME_EMAIL> format
  static Future<Map<String, dynamic>> registerUser({
    required String fullName,
    required String phone,
    required String password,
  }) async {
    try {
      final normalizedPhone = _normalizePhone(phone);
      final emailFromPhone = '$<EMAIL>';

      if (kDebugMode) {
        print('🔐 Attempting registration with email: $emailFromPhone');
        print('📱 Normalized phone: $normalizedPhone');
      }

      // Create new account using email derived from phone number
      final response = await _client.auth.signUp(
        email: emailFromPhone,
        password: password,
        data: {
          'full_name': fullName,
          'phone': normalizedPhone,
        },
      );

      final user = response.user;
      if (user != null) {
        if (kDebugMode) {
          print('✅ Auth user created successfully: ${user.id}');
          print('🔄 Creating user profile in users table...');
        }

        // Insert user information into users table
        await _client.from('users').upsert({
          'id': user.id, // Ensure this matches the auth user UUID
          'full_name': fullName,
          'phone': normalizedPhone,
          'email': emailFromPhone,
          'role': 'traveler', // Default role
          'is_leader': false,
          'is_verified': false, // Fixed column name
          'balance': 0.0, // Fixed column name
          'rating': 0.0,
          'total_trips': 0,
          'total_ratings': 0,
          'badges': [],
          'created_at': DateTime.now().toIso8601String(),
          'updated_at': DateTime.now().toIso8601String(),
        });

        if (kDebugMode) {
          print('✅ User registered and added to users table');
        }

        return {
          'success': true,
          'userId': user.id,
          'message': 'تم إنشاء الحساب بنجاح',
          'user': user,
        };
      } else {
        if (kDebugMode) {
          print('❌ Registration failed: user is null');
        }
        return {
          'success': false,
          'userId': null,
          'message': 'فشل في إنشاء الحساب',
          'user': null,
        };
      }
    } on AuthException catch (e) {
      if (kDebugMode) {
        print('❌ Auth error during registration: $e');
      }

      String errorMessage;
      switch (e.message.toLowerCase()) {
        case 'user already registered':
        case 'email address already in use':
          errorMessage = 'رقم الهاتف مسجل مسبقاً. يرجى تسجيل الدخول';
          break;
        case 'signup disabled':
          errorMessage = 'التسجيل غير متاح حالياً';
          break;
        case 'password should be at least 6 characters':
          errorMessage = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
          break;
        default:
          errorMessage = 'فشل في إنشاء الحساب: ${e.message}';
      }

      return {
        'success': false,
        'userId': null,
        'message': errorMessage,
        'user': null,
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ General error during registration: $e');
      }
      return {
        'success': false,
        'userId': null,
        'message': 'حدث خطأ غير متوقع. حاول مرة أخرى',
        'user': null,
      };
    }
  }

  /// Login user with phone number and password
  static Future<Map<String, dynamic>> loginUser({
    required String phone,
    required String password,
  }) async {
    try {
      final normalizedPhone = _normalizePhone(phone);
      final emailFromPhone = '$<EMAIL>';

      if (kDebugMode) {
        print('🔐 Attempting login with email: $emailFromPhone');
      }

      // Sign in using email derived from phone number
      final response = await _client.auth.signInWithPassword(
        email: emailFromPhone,
        password: password,
      );

      final user = response.user;
      if (user != null) {
        if (kDebugMode) {
          print('✅ Login successful for user: ${user.id}');
        }

        return {
          'success': true,
          'userId': user.id,
          'message': 'تم تسجيل الدخول بنجاح',
          'user': user,
        };
      } else {
        return {
          'success': false,
          'userId': null,
          'message': 'فشل في تسجيل الدخول',
          'user': null,
        };
      }
    } on AuthException catch (e) {
      if (kDebugMode) {
        print('❌ Auth error during login: $e');
      }

      String errorMessage;
      switch (e.message.toLowerCase()) {
        case 'invalid login credentials':
          errorMessage = 'رقم الهاتف أو كلمة المرور غير صحيحة';
          break;
        case 'email not confirmed':
          errorMessage = 'يجب تأكيد البريد الإلكتروني أولاً';
          break;
        case 'too many requests':
          errorMessage = 'محاولات كثيرة. حاول مرة أخرى لاحقاً';
          break;
        default:
          errorMessage = 'خطأ في تسجيل الدخول: ${e.message}';
      }

      return {
        'success': false,
        'userId': null,
        'message': errorMessage,
        'user': null,
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ General error during login: $e');
      }
      return {
        'success': false,
        'userId': null,
        'message': 'حدث خطأ غير متوقع. حاول مرة أخرى',
        'user': null,
      };
    }
  }

  /// Check if user exists by phone number
  static Future<bool> checkUserExists(String phone) async {
    try {
      final normalizedPhone = _normalizePhone(phone);
      final emailFromPhone = '$<EMAIL>';

      // Try to get user by email
      final response = await _client
          .from('users')
          .select('id')
          .eq('email', emailFromPhone)
          .limit(1)
          .maybeSingle();

      return response != null;
    } catch (e) {
      if (kDebugMode) {
        print('Error checking user existence: $e');
      }
      return false;
    }
  }

  /// Get current authenticated user
  static User? get currentUser => _client.auth.currentUser;

  /// Sign out current user
  static Future<void> signOut() async {
    try {
      await _client.auth.signOut();
      if (kDebugMode) {
        print('✅ User signed out successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error during sign out: $e');
      }
      rethrow;
    }
  }
}
