import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

class CityHistory {
  final String fromCity;
  final String toCity;
  final DateTime searchTime;

  CityHistory({
    required this.fromCity,
    required this.toCity,
    required this.searchTime,
  });

  Map<String, dynamic> toJson() {
    return {
      'fromCity': fromCity,
      'toCity': toCity,
      'searchTime': searchTime.toIso8601String(),
    };
  }

  factory CityHistory.fromJson(Map<String, dynamic> json) {
    return CityHistory(
      fromCity: json['fromCity'],
      toCity: json['toCity'],
      searchTime: DateTime.parse(json['searchTime']),
    );
  }

  @override
  String toString() {
    return '$fromCity ⇌ $toCity';
  }
}

class CityHistoryService {
  static final CityHistoryService _instance = CityHistoryService._internal();
  factory CityHistoryService() => _instance;
  CityHistoryService._internal();

  static const String _historyKey = 'city_search_history';
  static const String _quickFiltersKey = 'quick_city_filters';
  static const int _maxHistoryItems = 5;
  static const int _maxQuickFilters = 5;

  SharedPreferences? _prefs;

  /// Initialize SharedPreferences
  Future<void> init() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  /// Add a new city search to history
  Future<void> addCitySearch(String fromCity, String toCity) async {
    await init();

    if (fromCity.trim().isEmpty || toCity.trim().isEmpty) return;

    try {
      final history = await getCityHistory();

      // Remove if already exists to avoid duplicates
      history.removeWhere((item) =>
          item.fromCity.toLowerCase() == fromCity.toLowerCase() &&
          item.toCity.toLowerCase() == toCity.toLowerCase());

      // Add to the beginning
      history.insert(
          0,
          CityHistory(
            fromCity: fromCity.trim(),
            toCity: toCity.trim(),
            searchTime: DateTime.now(),
          ));

      // Keep only the last N searches
      if (history.length > _maxHistoryItems) {
        history.removeRange(_maxHistoryItems, history.length);
      }

      // Save to preferences
      final jsonList = history.map((item) => item.toJson()).toList();
      await _prefs!.setString(_historyKey, jsonEncode(jsonList));

      // Update quick filters
      await _updateQuickFilters('$fromCity ⇌ $toCity');

      if (kDebugMode) {
        print('💾 Added city search: $fromCity → $toCity');
        print('📝 History count: ${history.length}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to add city search: $e');
      }
    }
  }

  /// Get city search history
  Future<List<CityHistory>> getCityHistory() async {
    await init();

    try {
      final jsonString = _prefs!.getString(_historyKey);
      if (jsonString == null || jsonString.isEmpty) {
        return [];
      }

      final jsonList = jsonDecode(jsonString) as List;
      final history =
          jsonList.map((json) => CityHistory.fromJson(json)).toList();

      if (kDebugMode) {
        print('📖 Retrieved city history: ${history.length} items');
      }

      return history;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to get city history: $e');
      }
      return [];
    }
  }

  /// Clear all city history
  Future<void> clearCityHistory() async {
    await init();

    try {
      await _prefs!.remove(_historyKey);

      if (kDebugMode) {
        print('🧹 Cleared all city history');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to clear city history: $e');
      }
    }
  }

  /// Remove a specific city search from history
  Future<void> removeCitySearch(String fromCity, String toCity) async {
    await init();

    try {
      final history = await getCityHistory();
      history.removeWhere((item) =>
          item.fromCity.toLowerCase() == fromCity.toLowerCase() &&
          item.toCity.toLowerCase() == toCity.toLowerCase());

      final jsonList = history.map((item) => item.toJson()).toList();
      await _prefs!.setString(_historyKey, jsonEncode(jsonList));

      if (kDebugMode) {
        print('🗑️ Removed city search: $fromCity → $toCity');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to remove city search: $e');
      }
    }
  }

  /// Get recent city pairs as formatted strings
  Future<List<String>> getRecentCityPairs() async {
    final history = await getCityHistory();
    return history.map((item) => item.toString()).toList();
  }

  /// Check if a city pair exists in history
  Future<bool> hasCityPair(String fromCity, String toCity) async {
    final history = await getCityHistory();
    return history.any((item) =>
        item.fromCity.toLowerCase() == fromCity.toLowerCase() &&
        item.toCity.toLowerCase() == toCity.toLowerCase());
  }

  /// Get the most recent city search
  Future<CityHistory?> getLastCitySearch() async {
    final history = await getCityHistory();
    return history.isNotEmpty ? history.first : null;
  }

  /// Get history statistics for debugging
  Future<Map<String, dynamic>> getHistoryStats() async {
    final history = await getCityHistory();

    return {
      'total_searches': history.length,
      'max_history_items': _maxHistoryItems,
      'recent_searches': history.map((item) => item.toString()).toList(),
      'preferences_initialized': _prefs != null,
    };
  }

  /// Generate a greeting message with recent searches
  Future<String> generateHistoryGreeting() async {
    final history = await getCityHistory();

    if (history.isEmpty) {
      return 'مرحبا بيك! قول ليا فين بغيتي تمشي؟';
    }

    final lastSearch = history.first;
    return 'مرحبا! آخر بحث ديالك كان: ${lastSearch.toString()}. واش بغيتي تعاود نفس الرحلة؟';
  }

  /// Get quick filters (most frequent searches)
  Future<List<String>> getQuickFilters() async {
    try {
      await init();
      final filtersJson = _prefs!.getStringList(_quickFiltersKey) ?? [];

      if (kDebugMode) {
        print('🔍 Retrieved ${filtersJson.length} quick filters');
      }

      return filtersJson;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to get quick filters: $e');
      }
      return [];
    }
  }

  /// Update quick filters based on search frequency
  Future<void> _updateQuickFilters(String searchEntry) async {
    try {
      final currentFilters = await getQuickFilters();

      // Remove if already exists
      currentFilters.remove(searchEntry);

      // Add to beginning
      currentFilters.insert(0, searchEntry);

      // Keep only the most recent/frequent items
      if (currentFilters.length > _maxQuickFilters) {
        currentFilters.removeRange(_maxQuickFilters, currentFilters.length);
      }

      // Save to preferences
      await _prefs!.setStringList(_quickFiltersKey, currentFilters);

      if (kDebugMode) {
        print('🔍 Updated quick filters: ${currentFilters.length} items');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to update quick filters: $e');
      }
    }
  }

  /// Clear quick filters
  Future<void> clearQuickFilters() async {
    try {
      await init();
      await _prefs!.remove(_quickFiltersKey);

      if (kDebugMode) {
        print('🧹 Cleared quick filters');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to clear quick filters: $e');
      }
    }
  }
}
