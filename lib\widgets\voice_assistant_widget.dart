import 'package:flutter/material.dart';
import '../constants/app_theme.dart';
import '../services/voice_assistant_service.dart';

class VoiceAssistantWidget extends StatefulWidget {
  final VoidCallback? onTap;
  final VoiceAssistantState state;
  final double soundLevel;
  final double size;

  const VoiceAssistantWidget({
    super.key,
    this.onTap,
    required this.state,
    this.soundLevel = 0.0,
    this.size = 200.0,
  });

  @override
  State<VoiceAssistantWidget> createState() => _VoiceAssistantWidgetState();
}

class _VoiceAssistantWidgetState extends State<VoiceAssistantWidget>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _glowController;
  late AnimationController _rotationController;

  late Animation<double> _pulseAnimation;
  late Animation<double> _glowAnimation;
  late Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    // Pulse animation for listening state
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.4,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    // Glow animation for active states
    _glowController = AnimationController(
      duration: const Duration(milliseconds: 1800),
      vsync: this,
    );
    _glowAnimation = Tween<double>(
      begin: 0.2,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _glowController,
      curve: Curves.easeInOut,
    ));

    // Rotation animation for processing state
    _rotationController = AnimationController(
      duration: const Duration(milliseconds: 2500),
      vsync: this,
    );
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _rotationController,
      curve: Curves.linear,
    ));
  }

  @override
  void didUpdateWidget(VoiceAssistantWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.state != widget.state) {
      _updateAnimationsForState();
    }
  }

  void _updateAnimationsForState() {
    switch (widget.state) {
      case VoiceAssistantState.idle:
        _pulseController.stop();
        _glowController.stop();
        _rotationController.stop();
        break;
      case VoiceAssistantState.listening:
        _pulseController.repeat(reverse: true);
        _glowController.repeat(reverse: true);
        _rotationController.stop();
        break;
      case VoiceAssistantState.processing:
        _pulseController.stop();
        _glowController.forward();
        _rotationController.repeat();
        break;
      case VoiceAssistantState.speaking:
        _pulseController.repeat(reverse: true);
        _glowController.forward();
        _rotationController.stop();
        break;
      case VoiceAssistantState.error:
        _pulseController.stop();
        _glowController.stop();
        _rotationController.stop();
        break;
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _glowController.dispose();
    _rotationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap,
      child: SizedBox(
        width: widget.size,
        height: widget.size,
        child: Stack(
          alignment: Alignment.center,
          children: [
            // Animated ripple effects
            _buildRippleEffects(),

            // Outer glow effect
            _buildGlowEffect(),

            // Sound level visualization (for listening state)
            if (widget.state == VoiceAssistantState.listening)
              _buildSoundLevelVisualization(),

            // Main assistant circle
            _buildMainCircle(),

            // State-specific overlays
            _buildStateOverlay(),
          ],
        ),
      ),
    );
  }

  Widget _buildRippleEffects() {
    if (widget.state != VoiceAssistantState.listening &&
        widget.state != VoiceAssistantState.speaking) {
      return const SizedBox.shrink();
    }

    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Stack(
          alignment: Alignment.center,
          children: [
            // Outer ripple
            Container(
              width: widget.size * (1.0 + _pulseAnimation.value * 0.5),
              height: widget.size * (1.0 + _pulseAnimation.value * 0.5),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: _getStateColor().withValues(
                    alpha: (1.0 - _pulseAnimation.value) * 0.4,
                  ),
                  width: 2,
                ),
              ),
            ),
            // Middle ripple
            Container(
              width: widget.size * (1.0 + _pulseAnimation.value * 0.3),
              height: widget.size * (1.0 + _pulseAnimation.value * 0.3),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: _getStateColor().withValues(
                    alpha: (1.0 - _pulseAnimation.value) * 0.6,
                  ),
                  width: 1.5,
                ),
              ),
            ),
            // Inner ripple
            Container(
              width: widget.size * (1.0 + _pulseAnimation.value * 0.1),
              height: widget.size * (1.0 + _pulseAnimation.value * 0.1),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: _getStateColor().withValues(
                    alpha: (1.0 - _pulseAnimation.value) * 0.8,
                  ),
                  width: 1,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildGlowEffect() {
    return AnimatedBuilder(
      animation: _glowAnimation,
      builder: (context, child) {
        return Container(
          width: widget.size,
          height: widget.size,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: _getStateColor()
                    .withValues(alpha: _glowAnimation.value * 0.4),
                blurRadius: 40 * _glowAnimation.value,
                spreadRadius: 15 * _glowAnimation.value,
              ),
              BoxShadow(
                color: _getStateColor()
                    .withValues(alpha: _glowAnimation.value * 0.2),
                blurRadius: 60 * _glowAnimation.value,
                spreadRadius: 25 * _glowAnimation.value,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSoundLevelVisualization() {
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        final soundScale = 1.0 + (widget.soundLevel * 0.5);
        return Transform.scale(
          scale: _pulseAnimation.value * soundScale,
          child: Container(
            width: widget.size * 0.9,
            height: widget.size * 0.9,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: AppColors.primary.withValues(alpha: 0.3),
                width: 2,
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildMainCircle() {
    return AnimatedBuilder(
      animation: Listenable.merge([_pulseAnimation, _rotationAnimation]),
      builder: (context, child) {
        return Transform.scale(
          scale: widget.state == VoiceAssistantState.listening
              ? _pulseAnimation.value * 0.95
              : 1.0,
          child: Transform.rotate(
            angle: widget.state == VoiceAssistantState.processing
                ? _rotationAnimation.value * 2 * 3.14159
                : 0.0,
            child: Container(
              width: widget.size * 0.65,
              height: widget.size * 0.65,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: RadialGradient(
                  center: const Alignment(-0.3, -0.3),
                  radius: 1.2,
                  colors: [
                    _getStateColor().withValues(alpha: 0.9),
                    _getStateColor(),
                    _getStateColor().withValues(alpha: 0.8),
                  ],
                  stops: const [0.0, 0.7, 1.0],
                ),
                boxShadow: [
                  BoxShadow(
                    color: _getStateColor().withValues(alpha: 0.5),
                    blurRadius: 25,
                    spreadRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 15,
                    spreadRadius: 2,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: Container(
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.white.withValues(alpha: 0.2),
                      Colors.transparent,
                      Colors.black.withValues(alpha: 0.1),
                    ],
                  ),
                ),
                child: Icon(
                  _getStateIcon(),
                  size: widget.size * 0.22,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildStateOverlay() {
    switch (widget.state) {
      case VoiceAssistantState.processing:
        return Container(
          width: widget.size * 0.5,
          height: widget.size * 0.5,
          child: CircularProgressIndicator(
            strokeWidth: 3,
            valueColor: AlwaysStoppedAnimation<Color>(
                Colors.white.withValues(alpha: 0.8)),
          ),
        );
      case VoiceAssistantState.error:
        return Container(
          width: widget.size * 0.7,
          height: widget.size * 0.7,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.red.withValues(alpha: 0.1),
            border: Border.all(
              color: Colors.red.withValues(alpha: 0.5),
              width: 2,
            ),
          ),
        );
      default:
        return const SizedBox.shrink();
    }
  }

  Color _getStateColor() {
    switch (widget.state) {
      case VoiceAssistantState.idle:
        return AppColors.primary;
      case VoiceAssistantState.listening:
        return AppColors.secondary;
      case VoiceAssistantState.processing:
        return AppColors.accent;
      case VoiceAssistantState.speaking:
        return AppColors.primary;
      case VoiceAssistantState.error:
        return AppColors.error;
    }
  }

  IconData _getStateIcon() {
    switch (widget.state) {
      case VoiceAssistantState.idle:
        return Icons.mic;
      case VoiceAssistantState.listening:
        return Icons.mic;
      case VoiceAssistantState.processing:
        return Icons.psychology;
      case VoiceAssistantState.speaking:
        return Icons.volume_up;
      case VoiceAssistantState.error:
        return Icons.error_outline;
    }
  }
}

/// Status text widget to show current assistant state
class VoiceAssistantStatusText extends StatelessWidget {
  final VoiceAssistantState state;
  final String? customText;

  const VoiceAssistantStatusText({
    super.key,
    required this.state,
    this.customText,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 300),
      child: Text(
        customText ?? _getStatusText(),
        key: ValueKey(customText ?? state.toString()),
        style: theme.textTheme.titleMedium?.copyWith(
          color: AppColors.textSecondary,
          fontWeight: FontWeight.w500,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  String _getStatusText() {
    switch (state) {
      case VoiceAssistantState.idle:
        return 'اضغط للتحدث';
      case VoiceAssistantState.listening:
        return 'أستمع إليك...';
      case VoiceAssistantState.processing:
        return 'أفهم طلبك...';
      case VoiceAssistantState.speaking:
        return 'أتحدث معك...';
      case VoiceAssistantState.error:
        return 'حدث خطأ، حاول مرة أخرى';
    }
  }
}
