-- Supabase Profile Image Setup for Safarni App
-- Run these commands in your Supabase SQL editor to set up the profile_image bucket

-- =====================================================
-- STEP 1: CREATE PROFILE_IMAGE BUCKET
-- =====================================================

-- Create the profile_image bucket (public)
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'profile_image', 
  'profile_image', 
  true, -- Public bucket for easy access
  5242880, -- 5MB limit
  ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
)
ON CONFLICT (id) DO UPDATE SET
  name = 'profile_image',
  public = true,
  file_size_limit = 5242880,
  allowed_mime_types = ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];

-- =====================================================
-- STEP 2: SET UP STORAGE POLICIES
-- =====================================================

-- Allow authenticated users to upload their own profile images
CREATE POLICY "Users can upload their own profile images"
ON storage.objects
FOR INSERT
TO authenticated
WITH CHECK (
  bucket_id = 'profile_image' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);

-- Allow authenticated users to update their own profile images
CREATE POLICY "Users can update their own profile images"
ON storage.objects
FOR UPDATE
TO authenticated
USING (
  bucket_id = 'profile_image' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);

-- Allow authenticated users to delete their own profile images
CREATE POLICY "Users can delete their own profile images"
ON storage.objects
FOR DELETE
TO authenticated
USING (
  bucket_id = 'profile_image' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);

-- Allow public read access to all profile images (since bucket is public)
CREATE POLICY "Public read access to profile images"
ON storage.objects
FOR SELECT
TO public
USING (bucket_id = 'profile_image');

-- =====================================================
-- STEP 3: VERIFY USERS TABLE HAS PROFILE_IMAGE_URL COLUMN
-- =====================================================

-- Check if profile_image_url column exists, if not add it
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'users' 
        AND column_name = 'profile_image_url'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.users ADD COLUMN profile_image_url TEXT;
        RAISE NOTICE 'Added profile_image_url column to users table';
    ELSE
        RAISE NOTICE 'profile_image_url column already exists in users table';
    END IF;
END $$;

-- =====================================================
-- STEP 4: CREATE HELPER FUNCTIONS (OPTIONAL)
-- =====================================================

-- Function to get profile image URL with fallback
CREATE OR REPLACE FUNCTION get_profile_image_url(user_id UUID)
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    image_url TEXT;
BEGIN
    -- Get the profile image URL from users table
    SELECT profile_image_url INTO image_url
    FROM public.users
    WHERE id = user_id;
    
    -- Return the URL or null if not found
    RETURN image_url;
END;
$$;

-- Function to update profile image URL
CREATE OR REPLACE FUNCTION update_profile_image_url(user_id UUID, new_url TEXT)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Update the profile image URL
    UPDATE public.users
    SET profile_image_url = new_url,
        updated_at = NOW()
    WHERE id = user_id;
    
    -- Return true if update was successful
    RETURN FOUND;
END;
$$;

-- =====================================================
-- STEP 5: GRANT NECESSARY PERMISSIONS
-- =====================================================

-- Grant usage on the helper functions to authenticated users
GRANT EXECUTE ON FUNCTION get_profile_image_url(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION update_profile_image_url(UUID, TEXT) TO authenticated;

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Verify bucket was created
SELECT id, name, public, file_size_limit, allowed_mime_types
FROM storage.buckets
WHERE id = 'profile_image';

-- Verify policies were created
SELECT policyname, cmd, qual
FROM pg_policies
WHERE tablename = 'objects'
AND schemaname = 'storage'
AND policyname LIKE '%profile%';

-- Verify users table has profile_image_url column
SELECT column_name, data_type, is_nullable
FROM information_schema.columns
WHERE table_name = 'users'
AND column_name = 'profile_image_url'
AND table_schema = 'public';

-- =====================================================
-- USAGE EXAMPLES
-- =====================================================

/*
-- Example: Upload a profile image using the Flutter app
-- The image will be stored as: profile_image/profile_<user_id>.jpeg

-- Example: Get a user's profile image URL
SELECT get_profile_image_url('your-user-id-here');

-- Example: Update a user's profile image URL
SELECT update_profile_image_url('your-user-id-here', 'https://your-supabase-url.supabase.co/storage/v1/object/public/profile_image/profile_user123.jpeg');

-- Example: Query users with profile images
SELECT id, full_name, profile_image_url
FROM public.users
WHERE profile_image_url IS NOT NULL;
*/

-- =====================================================
-- TROUBLESHOOTING
-- =====================================================

/*
If you encounter issues:

1. Bucket not accessible:
   - Ensure the bucket is marked as public
   - Check that RLS policies are correctly set up

2. Upload fails:
   - Verify the user is authenticated
   - Check file size limits (5MB max)
   - Ensure MIME type is allowed

3. Images not loading:
   - Verify the public URL format
   - Check browser network tab for errors
   - Ensure CORS is properly configured

4. Permission denied:
   - Check RLS policies on storage.objects
   - Verify user authentication status
   - Ensure bucket permissions are correct
*/
