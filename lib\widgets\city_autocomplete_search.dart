import 'package:flutter/material.dart';
import '../constants/app_theme.dart';
import '../services/city_search_service.dart';
import '../services/search_preferences_service.dart';

class CityAutocompleteSearch extends StatefulWidget {
  final String? hintText;
  final Function(String)? onCitySelected;
  final Function(String)? onSearchChanged;
  final String? initialValue;
  final bool showRecentSearches;

  const CityAutocompleteSearch({
    super.key,
    this.hintText,
    this.onCitySelected,
    this.onSearchChanged,
    this.initialValue,
    this.showRecentSearches = true,
  });

  @override
  State<CityAutocompleteSearch> createState() => _CityAutocompleteSearchState();
}

class _CityAutocompleteSearchState extends State<CityAutocompleteSearch> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  final LayerLink _layerLink = LayerLink();

  final CitySearchService _citySearchService = CitySearchService();
  final SearchPreferencesService _preferencesService =
      SearchPreferencesService();

  OverlayEntry? _overlayEntry;
  List<String> _suggestions = [];
  List<String> _recentSearches = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _controller.text = widget.initialValue ?? '';
    _focusNode.addListener(_onFocusChanged);
    _loadRecentSearches();

    // Preload cities for better performance
    _citySearchService.preloadCities();
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    _hideOverlay();
    super.dispose();
  }

  void _onFocusChanged() {
    if (_focusNode.hasFocus) {
      _showSuggestionsOverlay();
    } else {
      _hideOverlay();
    }
  }

  Future<void> _loadRecentSearches() async {
    try {
      final recent = await _preferencesService.getRecentSearches();
      setState(() {
        _recentSearches = recent;
      });
    } catch (e) {
      // Handle error silently
    }
  }

  void _onSearchChanged(String query) async {
    widget.onSearchChanged?.call(query);

    if (query.trim().isEmpty) {
      setState(() {
        _suggestions = [];
        _isLoading = false;
      });
      _updateOverlay();
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final results = await _citySearchService.searchCities(query);
      if (mounted) {
        setState(() {
          _suggestions = results;
          _isLoading = false;
        });
        _updateOverlay();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _suggestions = [];
          _isLoading = false;
        });
        _updateOverlay();
      }
    }
  }

  void _onCitySelected(String city) async {
    _controller.text = city;
    _hideOverlay();
    _focusNode.unfocus();

    // Add to recent searches
    await _preferencesService.addRecentSearch(city);
    await _loadRecentSearches();

    widget.onCitySelected?.call(city);
  }

  void _showSuggestionsOverlay() {
    if (_overlayEntry != null) return;

    _overlayEntry = _createOverlayEntry();
    Overlay.of(context).insert(_overlayEntry!);
  }

  void _hideOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  void _updateOverlay() {
    if (_overlayEntry != null) {
      _overlayEntry!.markNeedsBuild();
    }
  }

  OverlayEntry _createOverlayEntry() {
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final size = renderBox.size;

    return OverlayEntry(
      builder: (context) => Positioned(
        width: size.width,
        child: CompositedTransformFollower(
          link: _layerLink,
          showWhenUnlinked: false,
          offset: Offset(0.0, size.height + 5.0),
          child: Material(
            elevation: 8.0,
            borderRadius: BorderRadius.circular(12),
            color: AppColors.surface,
            shadowColor: AppColors.primary.withValues(alpha: 0.1),
            child: _buildSuggestionsList(),
          ),
        ),
      ),
    );
  }

  Widget _buildSuggestionsList() {
    final bool showRecent = _controller.text.trim().isEmpty &&
        widget.showRecentSearches &&
        _recentSearches.isNotEmpty;

    final List<String> itemsToShow =
        showRecent ? _recentSearches : _suggestions;

    if (_isLoading) {
      return Container(
        padding: const EdgeInsets.all(16),
        child: const Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
            SizedBox(width: 12),
            Text('جاري البحث...',
                style: TextStyle(color: AppColors.textSecondary)),
          ],
        ),
      );
    }

    if (itemsToShow.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(16),
        child: Text(
          showRecent ? 'لا توجد عمليات بحث حديثة' : 'لم يتم العثور على مدن',
          style: const TextStyle(color: AppColors.textSecondary),
          textAlign: TextAlign.center,
        ),
      );
    }

    return Container(
      constraints: const BoxConstraints(maxHeight: 200),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (showRecent) ...[
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: const Row(
                children: [
                  Icon(Icons.history, size: 16, color: AppColors.textSecondary),
                  SizedBox(width: 8),
                  Text(
                    'آخر عمليات البحث',
                    style: TextStyle(
                      fontSize: 12,
                      color: AppColors.textSecondary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
            const Divider(height: 1),
          ],
          Flexible(
            child: ListView.builder(
              padding: EdgeInsets.zero,
              shrinkWrap: true,
              itemCount: itemsToShow.length,
              itemBuilder: (context, index) {
                final city = itemsToShow[index];
                final isRecent = showRecent;

                return ListTile(
                  dense: true,
                  leading: Icon(
                    isRecent ? Icons.history : Icons.location_on,
                    size: 20,
                    color:
                        isRecent ? AppColors.textSecondary : AppColors.primary,
                  ),
                  title: Text(
                    city,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  onTap: () => _onCitySelected(city),
                  trailing: isRecent
                      ? IconButton(
                          icon: const Icon(Icons.close, size: 16),
                          onPressed: () async {
                            await _preferencesService.removeRecentSearch(city);
                            await _loadRecentSearches();
                            _updateOverlay();
                          },
                        )
                      : null,
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: AppColors.primary.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: TextField(
          controller: _controller,
          focusNode: _focusNode,
          textDirection: TextDirection.rtl,
          textAlign: TextAlign.right,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
          decoration: InputDecoration(
            hintText: widget.hintText ?? 'بحث عن مدينة...',
            hintStyle: const TextStyle(
              color: AppColors.textSecondary,
              fontWeight: FontWeight.normal,
            ),
            prefixIcon: const Icon(
              Icons.search,
              color: AppColors.primary,
              size: 24,
            ),
            suffixIcon: _controller.text.isNotEmpty
                ? IconButton(
                    icon:
                        const Icon(Icons.clear, color: AppColors.textSecondary),
                    onPressed: () {
                      _controller.clear();
                      _onSearchChanged('');
                      widget.onCitySelected?.call('');
                    },
                  )
                : null,
            filled: true,
            fillColor: AppColors.surface,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide.none,
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(
                color: AppColors.border,
                width: 1,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(
                color: AppColors.primary,
                width: 2,
              ),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 16,
            ),
          ),
          onChanged: _onSearchChanged,
        ),
      ),
    );
  }
}
