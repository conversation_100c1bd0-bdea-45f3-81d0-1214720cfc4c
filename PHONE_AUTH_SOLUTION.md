# حل مشكلة تسجيل الدخول برقم الهاتف في Supabase

## 🔍 المشكلة الأصلية
كانت المشكلة في عدم تطابق منطق التسجيل مع منطق تسجيل الدخول:
- **التسجيل**: ينشئ email عشوائي (`<EMAIL>`)
- **تسجيل الدخول**: يبحث عن email في جدول المستخدمين

## ✅ الحل المطبق

### 1. توحيد منطق تحويل رقم الهاتف إلى Email
```dart
static String _phoneToEmail(String phone) {
  // إزالة جميع الأحرف غير الرقمية
  final cleanPhone = phone.replaceAll(RegExp(r'[^\d]'), '');
  
  // تحويل إلى تنسيق موحد: إزالة +212 أو 0 من البداية
  String normalizedPhone = cleanPhone;
  if (normalizedPhone.startsWith('212')) {
    normalizedPhone = normalizedPhone.substring(3);
  } else if (normalizedPhone.startsWith('0')) {
    normalizedPhone = normalizedPhone.substring(1);
  }
  
  // إنشاء تنسيق email: <EMAIL>
  return '$<EMAIL>';
}
```

### 2. تحسين دالة التسجيل
```dart
static Future<Map<String, dynamic>> signUpWithPhone({
  required String phone,
  required String password,
  required String fullName,
}) async {
  try {
    // تحويل رقم الهاتف إلى تنسيق email لـ Supabase auth
    final emailFromPhone = _phoneToEmail(phone);

    // التسجيل باستخدام email المحول
    final response = await _client.auth.signUp(
      email: emailFromPhone,
      password: password,
    );

    if (response.user != null) {
      await _createUserProfile(
        userId: response.user!.id,
        phone: phone,
        fullName: fullName,
        email: emailFromPhone,
      );

      return {
        'success': true,
        'userId': response.user!.id,
      };
    }
    // ...
  }
}
```

### 3. إصلاح دالة تسجيل الدخول
```dart
static Future<Map<String, dynamic>> signInWithPhone({
  required String phone,
  required String password,
}) async {
  try {
    // فحص وجود المستخدم أولاً
    final userExists = await checkUserExists(phone);
    if (!userExists) {
      return {
        'success': false,
        'message': 'رقم الهاتف غير مسجل. يرجى إنشاء حساب جديد',
      };
    }

    // تحويل رقم الهاتف إلى تنسيق email
    final emailFromPhone = _phoneToEmail(phone);

    // تسجيل الدخول مباشرة باستخدام email المحول
    final authResponse = await _client.auth.signInWithPassword(
      email: emailFromPhone,
      password: password,
    );

    if (authResponse.user != null) {
      return {
        'success': true,
        'userId': authResponse.user!.id,
        'message': 'تم تسجيل الدخول بنجاح',
      };
    }
    // ...
  } on AuthException catch (e) {
    // معالجة أخطاء Supabase بشكل مفصل
    String errorMessage;
    switch (e.message.toLowerCase()) {
      case 'invalid login credentials':
        errorMessage = 'رقم الهاتف أو كلمة المرور غير صحيحة';
        break;
      case 'email not confirmed':
        errorMessage = 'يجب تأكيد البريد الإلكتروني أولاً';
        break;
      case 'too many requests':
        errorMessage = 'محاولات كثيرة. حاول مرة أخرى لاحقاً';
        break;
      default:
        errorMessage = 'خطأ في تسجيل الدخول: ${e.message}';
    }
    
    return {
      'success': false,
      'message': errorMessage,
    };
  }
}
```

### 4. دالة فحص وجود المستخدم
```dart
static Future<bool> checkUserExists(String phone) async {
  try {
    final userResponse = await _client
        .from('users')
        .select('id')
        .eq('phone', phone)
        .maybeSingle();
    
    return userResponse != null;
  } catch (e) {
    return false;
  }
}
```

### 5. تحسين AuthProvider
```dart
Future<bool> signIn({
  required String phone,
  required String password,
  String? userType,
}) async {
  _setLoading(true);
  _clearError();

  try {
    final response = await SupabaseService.signInWithPhone(
      phone: phone,
      password: password,
    );

    if (response['success']) {
      await _loadUserProfile(response['userId']);
      _setLoading(false);
      return true;
    } else {
      // استخدام رسالة الخطأ المفصلة من SupabaseService
      _setError(response['message'] ?? 'فشل في تسجيل الدخول');
      _setLoading(false);
      return false;
    }
  } catch (e) {
    _setError('حدث خطأ غير متوقع. حاول مرة أخرى');
    _setLoading(false);
    return false;
  }
}
```

## 🎯 كيفية عمل النظام

### مثال عملي:
1. **رقم الهاتف المدخل**: `**********`
2. **التنظيف**: `**********` → `612345678`
3. **تحويل إلى Email**: `<EMAIL>`
4. **التسجيل/الدخول**: يستخدم `<EMAIL>` مع كلمة المرور

### أرقام هواتف مختلفة - نفس النتيجة:
- `**********` → `<EMAIL>`
- `+212612345678` → `<EMAIL>`
- `212612345678` → `<EMAIL>`
- `06 12 34 56 78` → `<EMAIL>`

## 🔧 رسائل الأخطاء المحسنة

| خطأ Supabase | الرسالة العربية |
|---------------|-----------------|
| `invalid login credentials` | رقم الهاتف أو كلمة المرور غير صحيحة |
| `email not confirmed` | يجب تأكيد البريد الإلكتروني أولاً |
| `too many requests` | محاولات كثيرة. حاول مرة أخرى لاحقاً |
| User not found | رقم الهاتف غير مسجل. يرجى إنشاء حساب جديد |

## 🧪 اختبار النظام

### استخدام صفحة الاختبار:
```dart
// في main.dart أو أي مكان للاختبار
Navigator.push(
  context,
  MaterialPageRoute(builder: (context) => const TestLoginPage()),
);
```

### خطوات الاختبار:
1. تأكد من وجود مستخدم مسجل في قاعدة البيانات
2. استخدم نفس رقم الهاتف وكلمة المرور
3. راقب الرسائل في وحدة التحكم (Debug mode)
4. تحقق من رسائل الخطأ المناسبة

## 📋 متطلبات قاعدة البيانات

### جدول users يجب أن يحتوي على:
```sql
- id (UUID, Primary Key)
- email (Text, Unique) -- يحتوي على <EMAIL>
- phone (Text, Unique) -- رقم الهاتف الأصلي
- full_name (Text)
- password (Text) -- يُدار بواسطة Supabase Auth
- role (Text, Default: 'traveler')
- created_at (Timestamp)
- updated_at (Timestamp)
```

## 🚀 المميزات الجديدة

1. **توحيد المنطق**: نفس طريقة تحويل الهاتف في التسجيل والدخول
2. **فحص مسبق**: التحقق من وجود المستخدم قبل محاولة تسجيل الدخول
3. **رسائل واضحة**: أخطاء مفصلة باللغة العربية
4. **مرونة في الإدخال**: يقبل أرقام الهواتف بتنسيقات مختلفة
5. **أمان محسن**: معالجة شاملة للأخطاء والاستثناءات

## 🔒 الأمان

- كلمات المرور تُدار بالكامل بواسطة Supabase Auth
- أرقام الهواتف تُحفظ في قاعدة البيانات للمرجعية
- Email المحول يُستخدم فقط للمصادقة
- فحص صحة رقم الهاتف المغربي قبل المعالجة
