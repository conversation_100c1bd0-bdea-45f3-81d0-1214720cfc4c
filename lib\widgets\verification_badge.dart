import 'package:flutter/material.dart';
import '../constants/app_theme.dart';

class VerificationBadge extends StatelessWidget {
  final double size;
  final bool showTooltip;
  final String? tooltipMessage;

  const VerificationBadge({
    super.key,
    this.size = 16.0,
    this.showTooltip = true,
    this.tooltipMessage,
  });

  @override
  Widget build(BuildContext context) {
    final badge = Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: Colors.blue,
        shape: BoxShape.circle,
        border: Border.all(
          color: Colors.white,
          width: 1.0,
        ),
      ),
      child: Icon(
        Icons.check,
        color: Colors.white,
        size: size * 0.7,
      ),
    );

    if (showTooltip) {
      return Tooltip(
        message: tooltipMessage ?? 'قائد رحلة موثق',
        child: badge,
      );
    }

    return badge;
  }
}

/// A widget that displays a user's name with an optional verification badge
class VerifiedUserName extends StatelessWidget {
  final String name;
  final bool isVerified;
  final TextStyle? textStyle;
  final double badgeSize;
  final MainAxisAlignment alignment;
  final CrossAxisAlignment crossAxisAlignment;

  const VerifiedUserName({
    super.key,
    required this.name,
    required this.isVerified,
    this.textStyle,
    this.badgeSize = 16.0,
    this.alignment = MainAxisAlignment.start,
    this.crossAxisAlignment = CrossAxisAlignment.center,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: alignment,
      crossAxisAlignment: crossAxisAlignment,
      mainAxisSize: MainAxisSize.min,
      children: [
        Flexible(
          child: Text(
            name,
            style: textStyle,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        if (isVerified) ...[
          const SizedBox(width: 4),
          VerificationBadge(size: badgeSize),
        ],
      ],
    );
  }
}
