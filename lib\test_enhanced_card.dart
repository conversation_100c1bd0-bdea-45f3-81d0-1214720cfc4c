import 'package:flutter/material.dart';
import 'constants/app_theme.dart';
import 'models/trip_model.dart';
import 'widgets/enhanced_trip_leader_card.dart';

/// Simple test page to verify the enhanced trip leader card works
class TestEnhancedCard extends StatelessWidget {
  const TestEnhancedCard({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Enhanced Card Test',
      theme: AppTheme.lightTheme,
      home: Scaffold(
        appBar: AppBar(
          title: const Text('Enhanced Trip Leader Card Test'),
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: <PERSON>umn(
            children: [
              const Text(
                'Enhanced Trip Leader Card Test',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 20),
              
              // Test Active Trip
              EnhancedTripLeaderCard(
                trip: _createTestTrip('active'),
              ),
              
              const SizedBox(height: 20),
              
              // Test Published Trip
              EnhancedTripLeaderCard(
                trip: _createTestTrip('published'),
              ),
              
              const SizedBox(height: 20),
              
              // Test Cancelled Trip
              EnhancedTripLeaderCard(
                trip: _createTestTrip('cancelled'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  TripModel _createTestTrip(String status) {
    return TripModel(
      id: 'test_${status}_${DateTime.now().millisecondsSinceEpoch}',
      leaderId: 'test_leader',
      title: 'رحلة تجريبية',
      description: 'رحلة تجريبية للاختبار',
      fromCity: 'الرباط',
      toCity: 'الدار البيضاء',
      departureDate: DateTime.now().add(const Duration(days: 1)),
      departureTime: '08:00',
      price: 50.0,
      totalSeats: 4,
      availableSeats: 2,
      status: status,
      carModel: 'تويوتا كامري',
      carPlate: 'أ-123456-ب',
      createdAt: DateTime.now().subtract(const Duration(minutes: 30)),
      updatedAt: DateTime.now(),
    );
  }
}

void main() {
  runApp(const TestEnhancedCard());
}
