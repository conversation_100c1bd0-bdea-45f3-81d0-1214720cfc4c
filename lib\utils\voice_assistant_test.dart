import 'package:flutter/foundation.dart';
import '../services/darija_nlp_service.dart';

/// Test utility for voice assistant functionality
class VoiceAssistantTest {
  static final DarijaNLPService _nlpService = DarijaNLPService();

  /// Test Darija NLP parsing with sample queries
  static void testDarijaParsing() {
    if (!kDebugMode) return;

    print('🧪 Testing Darija NLP Service...\n');

    final testQueries = [
      'بغيت نمشي من الدار البيضاء ل الرباط',
      'بغيت نسافر من فاس إلى مراكش',
      'من طنجة ل أكادير',
      'بغيت نروح ل الناظور من وجدة',
      'from casablanca to rabat',
      'i want to go from fes to marrakech',
      'الدار البيضاء الرباط', // Fallback test
      'بغيت نمشي', // Invalid test
      '', // Empty test
    ];

    for (final query in testQueries) {
      final result = _nlpService.parseQuery(query);
      print('Query: "$query"');
      print('Result: $result');
      print('Valid: ${result.isValid}');
      if (result.isValid) {
        print('Response: ${_nlpService.generateSuccessResponse(result)}');
      } else {
        print('Fallback: ${_nlpService.generateFallbackResponse()}');
      }
      print('---');
    }
  }

  /// Test supported cities
  static void testSupportedCities() {
    if (!kDebugMode) return;

    print('🏙️ Testing Supported Cities...\n');

    final cities = _nlpService.getSupportedCities();
    print('Total supported cities: ${cities.length}');
    
    for (final city in cities.take(10)) {
      print('✅ $city');
    }
    
    print('\nTesting city support:');
    final testCities = ['الدار البيضاء', 'casablanca', 'casa', 'unknown_city'];
    for (final city in testCities) {
      final isSupported = _nlpService.isCitySupported(city);
      print('$city: ${isSupported ? "✅" : "❌"}');
    }
  }

  /// Run all tests
  static void runAllTests() {
    if (!kDebugMode) return;

    print('🚀 Running Voice Assistant Tests...\n');
    testDarijaParsing();
    print('\n');
    testSupportedCities();
    print('\n✅ All tests completed!');
  }
}
