# RLS Policy Fix for Driver License Upload - إصلاح سياسة RLS لرفع رخصة القيادة

## Problem Description / وصف المشكلة

The driver license upload is failing with a Row Level Security (RLS) policy violation:

```
Error uploading driver license: StorageException(message: new row violates row-level security policy, statusCode: 403, error: Unauthorized)
```

كان رفع رخصة القيادة يفشل مع انتهاك سياسة أمان مستوى الصف (RLS).

## Root Cause Analysis / تحليل السبب الجذري

### 1. RLS Policy Configuration Issue / مشكلة تكوين سياسة RLS

**Problem:**
- The RLS policies may not be properly configured in Supabase
- Conflicting or missing policies preventing file uploads
- Policy patterns not matching the actual file paths

### 2. Authentication Context / سياق المصادقة

**Problem:**
- User authentication state not properly verified
- User ID mismatch between request and authenticated user
- Missing authentication headers in storage requests

### 3. File Path Pattern Mismatch / عدم تطابق نمط مسار الملف

**Problem:**
- Upload path doesn't match RLS policy expectations
- Incorrect file naming convention
- Missing folder structure requirements

## Complete Solution / الحل الكامل

### 1. Reset and Fix RLS Policies / إعادة تعيين وإصلاح سياسات RLS

#### **Execute the Reset Script:**

Run the `RESET_DRIVER_LICENSE_POLICIES.sql` script in your Supabase SQL Editor:

```sql
-- Clean up existing policies
DROP POLICY IF EXISTS "Users can upload their own license documents" ON storage.objects;
-- ... (removes all conflicting policies)

-- Create new working policies
CREATE POLICY "driver_license_upload_policy"
ON storage.objects
FOR INSERT
WITH CHECK (
  bucket_id = 'driver-licenses' 
  AND auth.role() = 'authenticated'
  AND name ~ ('^licenses/license_' || auth.uid()::text || '\.(jpg|jpeg|png|webp)$')
);
```

#### **Key Policy Features:**
- ✅ **Exact pattern matching** using regex
- ✅ **User ID validation** with `auth.uid()`
- ✅ **File extension validation** (jpg, jpeg, png, webp)
- ✅ **Folder structure enforcement** (licenses/ prefix)

### 2. Enhanced Upload Method / طريقة رفع محسنة

#### **Added Debug Logging:**

```dart
static Future<String?> uploadDriverLicense({
  required XFile imageFile,
  required String userId,
}) async {
  try {
    // Authentication verification with logging
    final currentUser = _supabase.auth.currentUser;
    if (currentUser == null) {
      print('❌ Upload failed: User not authenticated');
      throw Exception('User not authenticated');
    }

    print('✅ User authenticated: ${currentUser.id}');
    print('📝 Requested userId: $userId');

    // Security validation
    if (currentUser.id != userId) {
      print('❌ Upload failed: User ID mismatch');
      throw Exception('User ID mismatch - security violation');
    }

    // Correct path construction
    final fileExtension = imageFile.name.split('.').last.toLowerCase();
    final fileName = 'license_$userId.$fileExtension';
    final fullPath = 'licenses/$fileName';

    print('📁 Upload path: $fullPath');
    print('🗂️ Bucket: $_driverLicensesBucket');

    // Upload with detailed logging
    final response = await _supabase.storage
        .from(_driverLicensesBucket)
        .uploadBinary(fullPath, imageBytes, fileOptions: uploadOptions);

    if (response.isNotEmpty) {
      print('✅ License uploaded successfully!');
      return fullPath;
    }
  } catch (e) {
    print('❌ Error uploading driver license: $e');
    if (e is StorageException) {
      print('📋 Status code: ${e.statusCode}');
      print('💬 Message: ${e.message}');
    }
    return null;
  }
}
```

### 3. Bucket Configuration / تكوين الدلو

#### **Correct Bucket Setup:**

```sql
-- Ensure bucket exists with proper configuration
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'driver-licenses', 
  'driver-licenses', 
  false, -- Private bucket
  5242880, -- 5MB limit
  ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
)
ON CONFLICT (id) DO UPDATE SET
  public = false,
  file_size_limit = 5242880,
  allowed_mime_types = ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
```

### 4. File Path Structure / هيكل مسار الملف

#### **Required Path Pattern:**

```
driver-licenses/
└── licenses/
    ├── license_550e8400-e29b-41d4-a716-446655440000.jpg
    ├── license_123e4567-e89b-12d3-a456-426614174000.png
    └── license_987fcdeb-51a2-43d1-9c4f-123456789abc.jpeg
```

#### **Path Construction Logic:**

```dart
// User ID from authenticated user
final userId = currentUser.id; // e.g., "550e8400-e29b-41d4-a716-446655440000"

// File extension from uploaded file
final fileExtension = imageFile.name.split('.').last.toLowerCase(); // e.g., "jpg"

// Construct filename
final fileName = 'license_$userId.$fileExtension'; // e.g., "license_550e8400-e29b-41d4-a716-446655440000.jpg"

// Full path with folder
final fullPath = 'licenses/$fileName'; // e.g., "licenses/license_550e8400-e29b-41d4-a716-446655440000.jpg"
```

## Troubleshooting Steps / خطوات استكشاف الأخطاء

### 1. Verify Authentication / التحقق من المصادقة

```dart
// Check if user is authenticated
final currentUser = Supabase.instance.client.auth.currentUser;
print('Current user: ${currentUser?.id}');
print('Is authenticated: ${currentUser != null}');
```

### 2. Test RLS Policies / اختبار سياسات RLS

```sql
-- Check if policies exist
SELECT policyname, cmd, qual 
FROM pg_policies 
WHERE schemaname = 'storage' 
  AND tablename = 'objects'
  AND policyname LIKE '%license%';

-- Test policy pattern matching
SELECT 'licenses/license_550e8400-e29b-41d4-a716-446655440000.jpg' ~ 
       ('^licenses/license_550e8400-e29b-41d4-a716-446655440000\.(jpg|jpeg|png|webp)$') 
       AS policy_match;
```

### 3. Verify Bucket Configuration / التحقق من تكوين الدلو

```sql
-- Check bucket settings
SELECT id, name, public, file_size_limit, allowed_mime_types
FROM storage.buckets 
WHERE id = 'driver-licenses';

-- Check RLS is enabled
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'storage' AND tablename = 'objects';
```

### 4. Debug Upload Process / تصحيح عملية الرفع

**Enable debug logging in the app:**

```dart
// In main.dart or app initialization
if (kDebugMode) {
  // Enable detailed Supabase logging
  Supabase.initialize(
    url: 'your-url',
    anonKey: 'your-key',
    debug: true, // Enable debug mode
  );
}
```

**Check console output for:**
- ✅ User authentication status
- ✅ File path construction
- ✅ Upload request details
- ❌ Error messages and status codes

## Expected Results / النتائج المتوقعة

### Successful Upload / رفع ناجح

**Console Output:**
```
✅ User authenticated: 550e8400-e29b-41d4-a716-446655440000
📝 Requested userId: 550e8400-e29b-41d4-a716-446655440000
📁 Upload path: licenses/license_550e8400-e29b-41d4-a716-446655440000.jpg
📄 File extension: jpg
🗂️ Bucket: driver-licenses
📊 File size: 245760 bytes
🚀 Starting upload to Supabase...
✅ License uploaded successfully!
📍 Path: licenses/license_550e8400-e29b-41d4-a716-446655440000.jpg
```

### Failed Upload / رفع فاشل

**Console Output:**
```
❌ Error uploading driver license: StorageException(...)
🔍 Error type: StorageException
📋 Status code: 403
💬 Message: new row violates row-level security policy
⚠️ Error details: Unauthorized
```

## Implementation Steps / خطوات التطبيق

### Step 1: Reset RLS Policies / إعادة تعيين سياسات RLS

1. **Open Supabase Dashboard** → SQL Editor
2. **Copy and paste** the `RESET_DRIVER_LICENSE_POLICIES.sql` script
3. **Execute the script** to clean up and recreate policies
4. **Verify policies** are created correctly

### Step 2: Update Application Code / تحديث كود التطبيق

1. **The enhanced upload method** is already implemented
2. **Debug logging** will help identify issues
3. **Test the upload flow** in driver activation

### Step 3: Test and Verify / اختبار والتحقق

1. **Enable debug mode** in the app
2. **Attempt license upload** in driver activation
3. **Check console logs** for detailed information
4. **Verify file appears** in Supabase Storage

### Step 4: Monitor and Debug / مراقبة وتصحيح

1. **Watch console output** during upload
2. **Check Supabase logs** for server-side errors
3. **Verify authentication** is working correctly
4. **Test with different file types** (jpg, png, etc.)

## Security Features / ميزات الأمان

### Authentication Validation / التحقق من المصادقة

- ✅ **User must be authenticated** before upload
- ✅ **User ID verification** prevents cross-user uploads
- ✅ **Session validation** ensures valid authentication

### RLS Policy Enforcement / إنفاذ سياسة RLS

- ✅ **Path pattern matching** ensures correct file structure
- ✅ **User isolation** prevents access to other users' files
- ✅ **File type validation** only allows image files
- ✅ **Size limits** prevent abuse (5MB max)

### Access Control / التحكم في الوصول

- ✅ **Private bucket** - no public access
- ✅ **Signed URLs only** for temporary access
- ✅ **Admin oversight** for verification purposes
- ✅ **Audit trail** through Supabase logs

## Conclusion / الخلاصة

The RLS policy issue should be resolved by:

1. **Resetting and recreating** RLS policies with correct patterns
2. **Enhanced upload method** with detailed debugging
3. **Proper authentication validation** and security checks
4. **Comprehensive error handling** and logging

Follow the implementation steps and monitor the debug output to identify and resolve any remaining issues.

يجب حل مشكلة سياسة RLS من خلال إعادة تعيين وإنشاء سياسات RLS مع الأنماط الصحيحة وطريقة رفع محسنة مع تصحيح مفصل والتحقق من المصادقة ومعالجة شاملة للأخطاء.
