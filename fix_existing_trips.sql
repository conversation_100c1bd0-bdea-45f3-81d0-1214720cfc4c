-- SQL to fix existing trips with NULL driver_id
-- Run this in your Supabase SQL editor to update existing trips

-- Update trips where driver_id is NULL but leader_id exists
UPDATE public.trips 
SET driver_id = leader_id 
WHERE driver_id IS NULL 
  AND leader_id IS NOT NULL;

-- Verify the update
SELECT 
    id,
    leader_id,
    driver_id,
    status,
    from_city,
    to_city,
    created_at
FROM public.trips 
WHERE driver_id IS NOT NULL
ORDER BY created_at DESC
LIMIT 10;

-- Check for any remaining trips with NULL driver_id
SELECT 
    COUNT(*) as trips_with_null_driver_id
FROM public.trips 
WHERE driver_id IS NULL;

-- Optional: Delete trips that still have NULL driver_id and leader_id (orphaned trips)
-- Uncomment the line below if you want to remove orphaned trips
-- DELETE FROM public.trips WHERE driver_id IS NULL AND leader_id IS NULL;
