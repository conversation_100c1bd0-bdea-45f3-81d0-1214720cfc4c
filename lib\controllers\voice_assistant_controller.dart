import 'dart:async';
import 'package:flutter/foundation.dart';
import '../services/voice_assistant_service.dart';
import '../services/darija_nlp_service.dart';
import '../services/city_history_service.dart';
import '../services/supabase_service.dart';
import '../models/trip_model.dart';

class VoiceAssistantController {
  final VoiceAssistantService _voiceService = VoiceAssistantService();
  final DarijaNLPService _nlpService = DarijaNLPService();
  final CityHistoryService _historyService = CityHistoryService();
  
  // State management
  VoiceAssistantState _currentState = VoiceAssistantState.idle;
  double _soundLevel = 0.0;
  String _recognizedText = '';
  String _statusMessage = '';
  List<TripModel> _foundTrips = [];
  bool _isSearching = false;
  TripQuery? _lastQuery;
  List<String> _recentCityPairs = [];

  // Stream controllers for UI updates
  final StreamController<VoiceAssistantState> _stateController = 
      StreamController<VoiceAssistantState>.broadcast();
  final StreamController<double> _soundLevelController = 
      StreamController<double>.broadcast();
  final StreamController<String> _textController = 
      StreamController<String>.broadcast();
  final StreamController<String> _statusController = 
      StreamController<String>.broadcast();
  final StreamController<List<TripModel>> _tripsController = 
      StreamController<List<TripModel>>.broadcast();
  final StreamController<bool> _searchingController = 
      StreamController<bool>.broadcast();

  // Getters
  VoiceAssistantState get currentState => _currentState;
  double get soundLevel => _soundLevel;
  String get recognizedText => _recognizedText;
  String get statusMessage => _statusMessage;
  List<TripModel> get foundTrips => _foundTrips;
  bool get isSearching => _isSearching;
  TripQuery? get lastQuery => _lastQuery;
  List<String> get recentCityPairs => _recentCityPairs;

  // Streams
  Stream<VoiceAssistantState> get stateStream => _stateController.stream;
  Stream<double> get soundLevelStream => _soundLevelController.stream;
  Stream<String> get textStream => _textController.stream;
  Stream<String> get statusStream => _statusController.stream;
  Stream<List<TripModel>> get tripsStream => _tripsController.stream;
  Stream<bool> get searchingStream => _searchingController.stream;

  /// Initialize the voice assistant controller
  Future<bool> initialize() async {
    try {
      // Initialize services
      final voiceInitialized = await _voiceService.initialize();
      if (!voiceInitialized) {
        _updateStatus('فشل في تهيئة المساعد الصوتي');
        _updateState(VoiceAssistantState.error);
        return false;
      }

      await _historyService.init();
      await _loadRecentHistory();

      // Listen to voice service streams
      _voiceService.stateStream.listen((state) {
        _updateState(state);
      });

      _voiceService.textStream.listen((text) {
        _updateRecognizedText(text);
        
        if (_currentState == VoiceAssistantState.processing) {
          _processVoiceQuery(text);
        }
      });

      _voiceService.soundLevelStream.listen((level) {
        _updateSoundLevel(level);
      });

      // Show greeting with history
      final greeting = await _historyService.generateHistoryGreeting();
      _updateStatus(greeting);

      if (kDebugMode) {
        print('✅ Voice Assistant Controller initialized successfully');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Voice Assistant Controller initialization error: $e');
      }
      _updateState(VoiceAssistantState.error);
      return false;
    }
  }

  /// Load recent city search history
  Future<void> _loadRecentHistory() async {
    try {
      final pairs = await _historyService.getRecentCityPairs();
      _recentCityPairs = pairs;
      
      if (kDebugMode) {
        print('📖 Loaded ${pairs.length} recent city pairs');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to load recent history: $e');
      }
    }
  }

  /// Handle voice assistant tap
  Future<void> onAssistantTap() async {
    switch (_currentState) {
      case VoiceAssistantState.idle:
      case VoiceAssistantState.error:
        await startListening();
        break;
      case VoiceAssistantState.listening:
        await stopListening();
        break;
      case VoiceAssistantState.speaking:
        await stopSpeaking();
        break;
      case VoiceAssistantState.processing:
        // Cannot interrupt processing
        break;
    }
  }

  /// Start listening for voice input
  Future<void> startListening() async {
    await _voiceService.startListening();
  }

  /// Stop listening
  Future<void> stopListening() async {
    await _voiceService.stopListening();
  }

  /// Stop speaking
  Future<void> stopSpeaking() async {
    await _voiceService.stopSpeaking();
  }

  /// Process voice query using NLP
  Future<void> _processVoiceQuery(String text) async {
    if (text.trim().isEmpty) return;

    _updateStatus('أفهم طلبك...');

    // Parse the query using NLP
    final query = _nlpService.parseQuery(text);
    _lastQuery = query;

    if (query.isValid) {
      // Generate success response
      final response = _nlpService.generateSuccessResponse(query);
      _updateStatus(response);
      
      // Speak the response
      await _voiceService.speak(response);
      
      // Search for trips
      await _searchTrips(query);
      
      // Add to history
      await _historyService.addCitySearch(query.fromCity!, query.toCity!);
      await _loadRecentHistory();
    } else {
      // Generate error response
      final response = _nlpService.generateErrorResponse();
      _updateStatus(response);
      
      // Speak the error response
      await _voiceService.speak(response);
    }
  }

  /// Search for trips based on query
  Future<void> _searchTrips(TripQuery query) async {
    if (!query.isValid) return;

    _updateSearching(true);
    _foundTrips = [];
    _tripsController.add(_foundTrips);

    try {
      // Search trips using Supabase
      final allTrips = await SupabaseService.getTrips();
      
      // Filter trips based on query
      final filteredTrips = allTrips.where((trip) {
        final matchesFrom = trip.fromCity.toLowerCase().contains(query.fromCity!.toLowerCase());
        final matchesTo = trip.toCity.toLowerCase().contains(query.toCity!.toLowerCase());
        return matchesFrom && matchesTo;
      }).toList();

      _foundTrips = filteredTrips;
      _tripsController.add(_foundTrips);
      _updateSearching(false);

      // Announce results
      String resultMessage;
      if (filteredTrips.isEmpty) {
        resultMessage = _nlpService.generateNoTripsResponse();
      } else {
        resultMessage = _nlpService.generateFoundTripsResponse(filteredTrips.length);
      }

      _updateStatus(resultMessage);
      await _voiceService.speak(resultMessage);

    } catch (e) {
      _updateSearching(false);
      final errorMessage = 'حدث خطأ أثناء البحث عن الرحلات';
      _updateStatus(errorMessage);
      await _voiceService.speak(errorMessage);
      
      if (kDebugMode) {
        print('❌ Trip search error: $e');
      }
    }
  }

  /// Retry last search
  Future<void> retrySearch() async {
    if (_lastQuery != null && _lastQuery!.isValid) {
      await _searchTrips(_lastQuery!);
    }
  }

  /// Search for a specific city pair from history
  Future<void> searchCityPair(String fromCity, String toCity) async {
    final query = TripQuery(
      fromCity: fromCity,
      toCity: toCity,
      originalText: 'من $fromCity ل $toCity',
      confidence: 1.0,
    );
    
    _lastQuery = query;
    await _searchTrips(query);
  }

  /// Update state and notify listeners
  void _updateState(VoiceAssistantState newState) {
    if (_currentState != newState) {
      _currentState = newState;
      _stateController.add(_currentState);
    }
  }

  /// Update sound level and notify listeners
  void _updateSoundLevel(double level) {
    _soundLevel = level;
    _soundLevelController.add(_soundLevel);
  }

  /// Update recognized text and notify listeners
  void _updateRecognizedText(String text) {
    _recognizedText = text;
    _textController.add(_recognizedText);
  }

  /// Update status message and notify listeners
  void _updateStatus(String message) {
    _statusMessage = message;
    _statusController.add(_statusMessage);
  }

  /// Update searching state and notify listeners
  void _updateSearching(bool searching) {
    _isSearching = searching;
    _searchingController.add(_isSearching);
  }

  /// Dispose resources
  void dispose() {
    _voiceService.dispose();
    _stateController.close();
    _soundLevelController.close();
    _textController.close();
    _statusController.close();
    _tripsController.close();
    _searchingController.close();
  }
}
